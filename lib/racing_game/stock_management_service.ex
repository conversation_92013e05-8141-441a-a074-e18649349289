defmodule RacingGame.StockManagementService do
  @moduledoc """
  股票管理服务 - 提供股票持仓重置和重新统计功能
  """

  require Logger
  alias RacingGame.{Stock, StockStatistics, PointsTransaction}
  alias Cypridina.Repo
  require Ash.Query

  @doc """
  重置所有股票总持仓数据
  """
  def reset_all_stock_holdings do
    Logger.info("开始重置所有股票总持仓数据...")

    Repo.transaction(fn ->
      # 删除所有股票持仓记录
      case Stock |> Ash.read!() do
        stocks when is_list(stocks) ->
          Enum.each(stocks, fn stock ->
            case Ash.destroy(stock) do
              :ok -> :ok
              {:error, error} ->
                Logger.error("删除股票持仓失败: #{inspect(error)}")
                Repo.rollback("删除股票持仓失败")
            end
          end)

        _ ->
          Logger.error("读取股票持仓数据失败")
          Repo.rollback("读取股票持仓数据失败")
      end

      # 删除所有股票统计数据
      case StockStatistics |> Ash.read!() do
        stats when is_list(stats) ->
          Enum.each(stats, fn stat ->
            case Ash.destroy(stat) do
              :ok -> :ok
              {:error, error} ->
                Logger.error("删除股票统计失败: #{inspect(error)}")
                Repo.rollback("删除股票统计失败")
            end
          end)

        _ ->
          Logger.error("读取股票统计数据失败")
          Repo.rollback("读取股票统计数据失败")
      end

      Logger.info("股票总持仓数据重置完成")
      :ok
    end)
  end

  @doc """
  根据股票买入卖出交易记录重新统计股票总持仓
  """
  def recalculate_stock_holdings_from_transactions do
    Logger.info("开始根据交易记录重新统计股票总持仓...")

    Repo.transaction(fn ->
      # 获取所有股票买入卖出交易记录
      transactions = PointsTransaction
      |> Ash.Query.filter(transaction_type in [:buy_stock, :sell_stock])
      |> Ash.Query.sort(inserted_at: :asc)
      |> Ash.read!()

      Logger.info("找到 #{length(transactions)} 条股票交易记录")

      # 按用户和动物分组统计
      user_stock_map = Enum.reduce(transactions, %{}, fn transaction, acc ->
        case transaction.extra_data do
          %{"racer_id" => racer_id, "quantity" => quantity} when is_binary(racer_id) and is_integer(quantity) ->
            user_id = transaction.user_id
            key = {user_id, racer_id}

            current_quantity = Map.get(acc, key, 0)

            new_quantity = case transaction.transaction_type do
              :buy_stock -> current_quantity + quantity
              :sell_stock -> current_quantity - quantity
            end

            Map.put(acc, key, new_quantity)

          _ ->
            Logger.warning("交易记录格式不正确: #{inspect(transaction)}")
            acc
        end
      end)

      Logger.info("统计出 #{map_size(user_stock_map)} 个用户-股票组合")

      # 创建股票持仓记录
      Enum.each(user_stock_map, fn {{user_id, racer_id}, quantity} ->
        if quantity > 0 do
          case Stock.add(%{
            user_id: user_id,
            racer_id: racer_id,
            amount: quantity
          }) do
            {:ok, _stock} ->
              Logger.debug("创建股票持仓: 用户 #{user_id}, 动物 #{racer_id}, 数量 #{quantity}")

            {:error, error} ->
              Logger.error("创建股票持仓失败: #{inspect(error)}")
              Repo.rollback("创建股票持仓失败")
          end
        end
      end)

      # 重新统计股票总量
      recalculate_stock_statistics_from_transactions(transactions)

      Logger.info("股票总持仓重新统计完成")
      :ok
    end)
  end

  @doc """
  根据交易记录重新统计股票统计数据
  """
  def recalculate_stock_statistics_from_transactions(transactions \\ nil) do
    Logger.info("开始重新统计股票统计数据...")

    transactions = transactions || (
      PointsTransaction
      |> Ash.Query.filter(transaction_type in [:buy_stock, :sell_stock])
      |> Ash.read!()
    )

    # 按动物分组统计
    racer_stats = Enum.reduce(transactions, %{}, fn transaction, acc ->
      case transaction.extra_data do
        %{"racer_id" => racer_id, "quantity" => quantity, "stock_price" => stock_price}
        when is_binary(racer_id) and is_integer(quantity) ->
          current_stats = Map.get(acc, racer_id, %{
            total_bought: 0,
            total_sold: 0,
            total_cost: Decimal.new(0),
            total_revenue: Decimal.new(0)
          })

          case transaction.transaction_type do
            :buy_stock ->
              cost_amount = Decimal.mult(Decimal.new(stock_price), Decimal.new(quantity))
              %{
                current_stats |
                total_bought: current_stats.total_bought + quantity,
                total_cost: Decimal.add(current_stats.total_cost, cost_amount)
              }

            :sell_stock ->
              revenue_amount = Decimal.mult(Decimal.new(stock_price), Decimal.new(quantity))
              %{
                current_stats |
                total_sold: current_stats.total_sold + quantity,
                total_revenue: Decimal.add(current_stats.total_revenue, revenue_amount)
              }
          end
          |> then(&Map.put(acc, racer_id, &1))

        _ ->
          acc
      end
    end)

    # 创建股票统计记录
    Enum.each(racer_stats, fn {racer_id, stats} ->
      # 先创建买入统计
      if stats.total_bought > 0 do
        case StockStatistics.update_on_buy(%{
          racer_id: racer_id,
          quantity: stats.total_bought,
          cost_amount: stats.total_cost
        }) do
          {:ok, _stock_stat} ->
            Logger.debug("创建股票买入统计: 动物 #{racer_id}, 买入 #{stats.total_bought}")

          {:error, error} ->
            Logger.error("创建股票买入统计失败: #{inspect(error)}")
            Repo.rollback("创建股票买入统计失败")
        end
      end

      # 再创建卖出统计
      if stats.total_sold > 0 do
        case StockStatistics.update_on_sell(%{
          racer_id: racer_id,
          quantity: stats.total_sold,
          sell_amount: stats.total_revenue
        }) do
          {:ok, _stock_stat} ->
            Logger.debug("创建股票卖出统计: 动物 #{racer_id}, 卖出 #{stats.total_sold}")

          {:error, error} ->
            Logger.error("创建股票卖出统计失败: #{inspect(error)}")
            Repo.rollback("创建股票卖出统计失败")
        end
      end
    end)

    Logger.info("股票统计数据重新统计完成")
    :ok
  end

  @doc """
  获取股票持仓统计摘要
  """
  def get_stock_holdings_summary do
    total_holdings = Stock |> Ash.read!() |> length()
    total_users = Stock |> Ash.read!() |> Enum.map(&(&1.user_id)) |> Enum.uniq() |> length()
    total_quantity = Stock |> Ash.read!() |> Enum.reduce(0, &(&1.quantity + &2))

    %{
      total_holdings: total_holdings,
      total_users: total_users,
      total_quantity: total_quantity
    }
  end

  @doc """
  获取股票统计摘要
  """
  def get_stock_statistics_summary do
    stats = StockStatistics |> Ash.read!()

    total_bought = Enum.reduce(stats, 0, &(&1.total_bought + &2))
    total_sold = Enum.reduce(stats, 0, &(&1.total_sold + &2))
    total_cost = Enum.reduce(stats, Decimal.new(0), &Decimal.add(&1.total_cost, &2))
    total_revenue = Enum.reduce(stats, Decimal.new(0), &Decimal.add(&1.total_revenue, &2))

    %{
      total_animals: length(stats),
      total_bought: total_bought,
      total_sold: total_sold,
      total_cost: total_cost,
      total_revenue: total_revenue,
      net_holdings: total_bought - total_sold
    }
  end
end
