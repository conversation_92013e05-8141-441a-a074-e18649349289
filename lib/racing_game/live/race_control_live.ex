defmodule RacingGame.Live.RaceControlLive do
  @moduledoc """
  比赛控制页面 - LiveView 实现，支持自动排名控制和用户拖拽排名
  """
  use CypridinaWeb, :live_view
  alias RacingGame.{Race, Main, Bet, StockManagementService}
  require Logger

  # 动物信息
  @animals [
    %{id: "A", name: "饿小宝", image: "饿小宝.png"},
    %{id: "B", name: "盒马", image: "盒马.png"},
    %{id: "C", name: "票票", image: "票票.png"},
    %{id: "D", name: "虾仔", image: "虾仔.png"},
    %{id: "E", name: "支小宝", image: "支小宝.png"},
    %{id: "F", name: "欢猩", image: "欢猩.png"}
  ]

  def mount(_params, _session, socket) do
    # 订阅比赛相关消息
    if connected?(socket) do
      Phoenix.PubSub.subscribe(Cypridina.PubSub, Main.race_topic())
    end

    # 获取当前比赛
    current_race = Main.get_current_race()

    # 获取股票统计数据
    stock_statistics = get_stock_statistics()

    {:ok,
     socket
     |> assign(:animals, @animals)
     |> assign(:current_race, current_race)
     |> assign(:total_bets, Main.get_total_bets())
     |> assign(:auto_ranking, [])
     |> assign(:user_preset_ranking, Main.get_user_preset_ranking())
     |> assign(:control_form, to_form(%{}, as: :control))
     |> assign(:stock_statistics, stock_statistics)}
  end

  def render(assigns) do
    ~H"""
    <div class="race-control-panel">
      <!-- 返回按钮 -->
      <div class="mb-4">
        <.link
          href="/admin_panel"
          class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          <!-- 返回箭头图标 -->
          <svg
            class="w-4 h-4 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M10 19l-7-7m0 0l7-7m-7 7h18"
            />
          </svg>
          返回管理后台
        </.link>
      </div>

    <!-- 页面标题 -->
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">比赛控制面板</h1>
        <p class="text-gray-600">管理当前比赛状态和控制游戏流程</p>
      </div>

    <!-- 当前比赛状态 - 模仿champion-selection样式 -->
      <div class="current-race-status mb-6">
        <h3 class="text-lg font-semibold mb-2">当前比赛状态</h3>
        <%= if @current_race do %>
          <div class="champion-selection">
            <div class="animal-grid">
              <%= for animal <- @animals do %>
                <div class="animal-card">
                  <div class="animal-display-area">
                    <img
                      src={"/images/#{animal.image}"}
                      class="animal-avatar"
                      alt={"#{animal.name}头像"}
                    />
                    <div class="animal-name">
                      {animal.name}
                    </div>

                    <div class="animal-bet-amount">
                      <span class="bet-label">身价:</span>
                      <span class="bet-value">
                        {Map.get(@current_race.bet_amount_map || %{}, animal.id, 100)}
                      </span>
                    </div>

                    <div class="total-bet-amount">
                      <span class="bet-label">总下注:</span>
                      <span class="bet-value">
                        {Map.get(@total_bets, animal.id, 0)}
                      </span>
                    </div>

                    <!-- 股票统计信息 -->
                    <% stock_stat = Map.get(@stock_statistics, animal.id, %{}) %>
                    <div class="stock-statistics">
                      <div class="stock-stat-row">
                        <span class="stat-label">持仓:</span>
                        <span class="stat-value net-position">
                          {Map.get(stock_stat, :net_position, 0)} 股
                        </span>
                      </div>
                      <div class="stock-stat-row">
                        <span class="stat-label">平均成本:</span>
                        <span class="stat-value average-cost">
                          {Float.round(Map.get(stock_stat, :average_cost, 0.0), 1)} 积分
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        <% else %>
          <p class="text-gray-500">暂无进行中的比赛</p>
        <% end %>
      </div>

    <!-- 比赛结果控制 -->
      <div class="race-control mb-6">
        <h3 class="text-lg font-semibold mb-2">比赛结果控制</h3>

    <!-- 自动排名显示 -->
        <%= if @auto_ranking != [] do %>
          <div class={"auto-ranking mb-4 p-4 bg-green-50 rounded-lg #{if @user_preset_ranking == [] or @user_preset_ranking == nil, do: "highlight-active", else: ""}"}>
            <h4 class="font-medium mb-2 text-green-800">系统自动排名 (基于总下注最少赔付)</h4>
            <div class="ranking-display flex flex-wrap gap-2">
              <%= for {animal_id, position} <- Enum.with_index(@auto_ranking, 1) do %>
                <% animal = Enum.find(@animals, &(&1.id == animal_id)) %>
                <div class="ranking-item flex items-center bg-white p-2 rounded border">
                  <span class="ranking-number bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm mr-2">
                    {position}
                  </span>
                  <img src={"/images/#{animal.image}"} class="w-8 h-8 mr-2" alt={animal.name} />
                  <span class="text-sm">{animal.name}</span>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>

    <!-- 用户拖拽排名 -->
        <div class={"user-ranking mb-4 p-4 bg-blue-50 rounded-lg #{if @user_preset_ranking != [] and @user_preset_ranking != nil, do: "highlight-active", else: ""}"}>
          <h4 class="font-medium mb-2 text-blue-800">用户预设排名 (拖拽调整)</h4>
          <div class="ranking-control">
            <div
              id="sortable-ranking"
              class="sortable-container flex flex-wrap gap-2"
              phx-hook="SortableRanking"
            >
              <%= if @user_preset_ranking != [] do %>
                <%= for animal_id <- @user_preset_ranking do %>
                  <% animal = Enum.find(@animals, fn a -> a.id == animal_id end) %>
                  <%= if animal do %>
                    <div
                      class="draggable-animal bg-white p-3 rounded border cursor-move hover:shadow-md transition-shadow"
                      data-animal-id={animal_id}
                    >
                      <div class="flex items-center">
                        <img src={"/images/#{animal.image}"} class="w-10 h-10 mr-2" alt={animal.name} />
                        <div>
                          <div class="font-medium text-sm">{animal.name}</div>
                          <div class="text-xs text-gray-500">拖拽排序</div>
                        </div>
                      </div>
                    </div>
                  <% end %>
                <% end %>
              <% else %>
                <%= for animal <- @animals do %>
                  <div
                    class="draggable-animal bg-white p-3 rounded border cursor-move hover:shadow-md transition-shadow"
                    data-animal-id={animal.id}
                  >
                    <div class="flex items-center">
                      <img src={"/images/#{animal.image}"} class="w-10 h-10 mr-2" alt={animal.name} />
                      <div>
                        <div class="font-medium text-sm">{animal.name}</div>
                        <div class="text-xs text-gray-500">拖拽排序</div>
                      </div>
                    </div>
                  </div>
                <% end %>
              <% end %>
            </div>
            <div class="mt-2 flex gap-2">
              <%!-- <button
                phx-click="apply_auto_ranking"
                class="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
              >
                应用自动排名
              </button> --%>
              <button
                phx-click="clear_user_ranking"
                class="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
              >
                清除用户预设排名
              </button>
            </div>
          </div>
        </div>

    <!-- 游戏控制功能 -->
        <%= if @current_race do %>
          <div class="game-control mb-4 p-4 bg-red-50 rounded-lg">
            <h4 class="font-medium mb-3 text-red-800">游戏控制</h4>
            <div class="control-buttons flex flex-wrap gap-3">
              <button
                phx-click="reset_animal_prices"
                class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                onclick="return confirm('确定要重置所有动物身价到预设值吗？')"
              >
                重置身价
              </button>

              <button
                phx-click="force_liquidate_stocks"
                class="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700"
                onclick="return confirm('确定要强制所有用户平仓吗？此操作将卖出所有股票并返还积分！')"
              >
                强制平仓
              </button>

              <%!-- <button
                phx-click="force_end_current_race"
                class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                onclick="return confirm('确定要强制结束当前比赛吗？')"
              >
                强制结束比赛
              </button> --%>
            </div>
          </div>
        <% end %>

        <!-- 股票管理功能 - 始终可用 -->
        <div class="stock-management mb-4 p-4 bg-purple-50 rounded-lg">
          <h4 class="font-medium mb-3 text-purple-800">股票管理</h4>
          <div class="control-buttons flex flex-wrap gap-3">
            <button
              phx-click="reset_stock_holdings"
              class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              onclick="return confirm('确定要重置所有股票持仓数据吗？此操作不可逆！')"
            >
              重置股票持仓
            </button>

            <button
              phx-click="recalculate_stock_holdings"
              class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              onclick="return confirm('确定要重新统计股票总持仓吗？')"
            >
              重新统计股票持仓
            </button>
          </div>
        </div>
      </div>
    <!-- 添加样式 -->
      <style>
        .champion-selection {
          padding: 8px;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(255, 140, 56, 0.2);
          overflow: visible;
          height: auto;
        }

        .animal-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 12px;
          padding: 8px;
        }

        .animal-card {
          background: linear-gradient(135deg, #fff8f0 0%, #fff4e6 100%);
          border: 2px solid #ffb273;
          border-radius: 12px;
          padding: 12px;
          text-align: center;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
        }

        .animal-display-area {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;
        }

        .animal-avatar {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          border: 3px solid #ff8c38;
          object-fit: cover;
        }

        .animal-name {
          font-weight: bold;
          color: #ff7a00;
          font-size: 14px;
        }

        .animal-bet-amount, .total-bet-amount {
          display: flex;
          justify-content: space-between;
          width: 100%;
          font-size: 12px;
        }

        .bet-label {
          color: #666;
        }

        .bet-value {
          font-weight: bold;
          color: #ff7a00;
        }

        /* 股票统计样式 */
        .stock-statistics {
          width: 100%;
          margin-top: 8px;
          padding-top: 8px;
          border-top: 1px solid #ffb273;
        }

        .stock-stat-row {
          display: flex;
          justify-content: space-between;
          width: 100%;
          font-size: 11px;
          margin-bottom: 2px;
        }

        .stat-label {
          color: #666;
          font-size: 10px;
        }

        .stat-value {
          font-weight: bold;
          font-size: 11px;
        }

        .stat-value.net-position {
          color: #2563eb;
        }

        .stat-value.average-cost {
          color: #7c3aed;
        }

        .sortable-container {
          min-height: 100px;
          border: 2px dashed #ddd;
          border-radius: 8px;
          padding: 10px;
        }

        .draggable-animal {
          transition: transform 0.2s ease;
        }

        .draggable-animal:hover {
          transform: translateY(-2px);
        }

        .draggable-animal.sortable-ghost {
          opacity: 0.5;
        }

        /* 高亮激活状态的红圈效果 */
        .highlight-active {
          position: relative;
          border: 3px solid #ef4444 !important;
          box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2), 0 0 20px rgba(239, 68, 68, 0.3) !important;
          animation: pulse-red 2s infinite;
        }

        .highlight-active::before {
          content: '';
          position: absolute;
          top: -6px;
          left: -6px;
          right: -6px;
          bottom: -6px;
          border: 2px solid #ef4444;
          border-radius: 12px;
          animation: pulse-border 2s infinite;
          pointer-events: none;
        }

        @keyframes pulse-red {
          0%, 100% {
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2), 0 0 20px rgba(239, 68, 68, 0.3);
          }
          50% {
            box-shadow: 0 0 0 6px rgba(239, 68, 68, 0.1), 0 0 30px rgba(239, 68, 68, 0.4);
          }
        }

        @keyframes pulse-border {
          0%, 100% {
            opacity: 1;
            transform: scale(1);
          }
          50% {
            opacity: 0.7;
            transform: scale(1.02);
          }
        }
      </style>
    </div>
    """
  end

  def handle_event("clear_user_ranking", _params, socket) do
    # 清除 Main 中的用户预设
    Main.clear_user_preset_ranking()

    {:noreply, assign(socket, :user_preset_ranking, [])}
  end

  def handle_event("update_ranking", %{"ranking" => ranking_list}, socket) do
    # 处理拖拽排序结果，设置到 Main
    Main.set_user_preset_ranking(ranking_list)

    {:noreply, assign(socket, :user_preset_ranking, ranking_list)}
  end

  def handle_event("apply_auto_ranking", _params, socket) do
    # 应用自动排名到用户预设
    auto_ranking = socket.assigns.auto_ranking
    Main.set_user_preset_ranking(auto_ranking)

    {:noreply, assign(socket, :user_preset_ranking, auto_ranking)}
  end

  def handle_event("reset_animal_prices", _params, socket) do
    case Main.reset_animal_prices() do
      :ok ->
        {:noreply,
         socket
         |> put_flash(:info, "身价将在下一把重置到预设值")}

      {:error, reason} ->
        Logger.error("重置动物身价失败: #{inspect(reason)}")
        {:noreply, put_flash(socket, :error, "重置失败，请重试")}
    end
  end

  def handle_event("force_liquidate_stocks", _params, socket) do
    case Main.force_liquidate_all_stocks() do
      {:ok, result} ->
        message =
          "强制平仓完成！清算了 #{result.total_users} 个用户的 #{result.total_stocks_liquidated} 份股票，返还 #{result.total_points_returned} 积分"

        {:noreply,
         socket
         |> put_flash(:info, message)
         |> assign(:current_race, Main.get_current_race())
         |> assign(:total_bets, Main.get_total_bets())}

      {:error, reason} ->
        Logger.error("强制平仓失败: #{inspect(reason)}")
        {:noreply, put_flash(socket, :error, "强制平仓失败，请重试")}
    end
  end

  def handle_event("reset_stock_holdings", _params, socket) do
    case StockManagementService.reset_all_stock_holdings() do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "股票总持仓数据重置成功")
         |> assign(:stock_statistics, get_stock_statistics())}

      {:error, reason} ->
        Logger.error("重置股票持仓失败: #{inspect(reason)}")
        {:noreply, put_flash(socket, :error, "重置股票持仓失败，请重试")}
    end
  end

  def handle_event("recalculate_stock_holdings", _params, socket) do
    case StockManagementService.recalculate_stock_holdings_from_transactions() do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "股票总持仓数据重新统计成功")
         |> assign(:stock_statistics, get_stock_statistics())}

      {:error, reason} ->
        Logger.error("重新统计股票持仓失败: #{inspect(reason)}")
        {:noreply, put_flash(socket, :error, "重新统计股票持仓失败，请重试")}
    end
  end

  def handle_event("force_end_current_race", _params, socket) do
    case socket.assigns.current_race do
      nil ->
        {:noreply, put_flash(socket, :error, "当前没有进行中的比赛")}

      race ->
        # 获取最终排名（优先级：用户预设 > 自动排名 > 随机）
        final_ranking = get_final_ranking(socket.assigns)

        case force_end_race_with_ranking(race, final_ranking) do
          {:ok, updated_race} ->
            {:noreply,
             socket
             |> put_flash(:info, "比赛已强制结束，排名：#{Enum.join(final_ranking, " > ")}")
             |> assign(:current_race, nil)
             |> assign(:total_bets, %{})
             |> assign(:auto_ranking, [])
             |> assign(:user_preset_ranking, [])}

          {:error, reason} ->
            Logger.error("强制结束比赛失败: #{inspect(reason)}")
            {:noreply, put_flash(socket, :error, "操作失败，请重试")}
        end
    end
  end

  # 处理统一的游戏数据更新事件
  def handle_info({:game_data_update, game_data}, socket) do
    # Logger.info(
    #   "🔄 [GAME_DATA_UPDATE] 收到游戏数据更新: #{inspect(game_data.event_type)}, 当前比赛: #{inspect(game_data.current_race)}"
    # )

    case game_data.event_type do
      :new_race ->
        {:noreply,
         socket
         |> assign(current_race: game_data.current_race)
         |> assign(total_bets: game_data.total_bets)
         |> assign(auto_ranking: game_data.ranking_control.auto_ranking || [])
         |> assign(user_preset_ranking: game_data.ranking_control.user_preset_ranking || [])}

      :race_started ->
        {:noreply,
          socket
          |> assign(current_race: game_data.current_race)
        }

      :race_ended ->
        {:noreply,
         socket
         |> assign(current_race: game_data.current_race)
         |> assign(total_bets: game_data.total_bets)
         |> assign(auto_ranking: [])
         |> assign(user_preset_ranking: [])}

      :animal_prices_reset ->
        {:noreply,
         socket
         |> assign(current_race: game_data.current_race)
         |> assign(total_bets: game_data.total_bets)}

      :force_liquidation_completed ->
        result = game_data.extra_data

        message =
          "系统强制平仓完成：#{result.total_users} 用户，#{result.total_stocks_liquidated} 股票，#{result.total_points_returned} 积分"

        {:noreply,
         socket
         |> put_flash(:info, message)
         |> assign(total_bets: game_data.total_bets)}

      :bet_placed ->
        # 当有新的下注时，实时更新总下注统计和自动排名
        current_race = socket.assigns.current_race

        if current_race && current_race.issue == game_data.current_race.issue do
          {:noreply,
           socket
           |> assign(total_bets: game_data.total_bets)
           |> assign(auto_ranking: game_data.ranking_control.auto_ranking || [])
           |> assign(user_preset_ranking: game_data.ranking_control.user_preset_ranking || [])}
        else
          {:noreply, socket}
        end

      :user_preset_ranking_changed ->
        # 用户预设排名发生变化
        ranking = game_data.extra_data.ranking
        Logger.info("🔄 [RACE_CONTROL] 收到用户预设排名变化: #{inspect(ranking)}")

        {:noreply,
         socket
         |> assign(user_preset_ranking: ranking)
         |> put_flash(:info, "用户预设排名已更新：#{Enum.join(ranking, " > ")}")}

      :user_preset_ranking_cleared ->
        # 用户预设排名被清除
        Logger.info("🔄 [RACE_CONTROL] 收到用户预设排名清除事件")

        {:noreply,
         socket
         |> assign(user_preset_ranking: [])
         |> put_flash(:info, "用户预设排名已清除")}

      _ ->
        {:noreply, socket}
    end
  end

  # 处理股票统计更新事件
  def handle_info({:stock_statistics_updated, data}, socket) do
    Logger.info("🔄 [RACE_CONTROL] 收到股票统计更新: #{inspect(data)}")

    # 重新获取股票统计数据
    stock_statistics = get_stock_statistics()

    {:noreply, assign(socket, :stock_statistics, stock_statistics)}
  end

  # 兼容旧的事件（逐步移除）
  def handle_info({:race_force_ended, race}, socket) do
    {:noreply,
     socket
     |> assign(current_race: nil)
     |> assign(total_bets: %{})
     |> assign(auto_ranking: [])
     |> assign(user_preset_ranking: [])}
  end

  # 私有函数
  defp get_final_ranking(assigns) do
    cond do
      assigns.user_preset_ranking != [] ->
        assigns.user_preset_ranking

      assigns.auto_ranking != [] ->
        assigns.auto_ranking

      true ->
        # 随机排名
        Enum.shuffle(["A", "B", "C", "D", "E", "F"])
    end
  end

  defp force_end_race_with_ranking(race, ranking) do
    # 生成默认的速度和时间数据
    default_speeds = %{
      "A" => 50.0,
      "B" => 48.0,
      "C" => 46.0,
      "D" => 44.0,
      "E" => 42.0,
      "F" => 40.0
    }

    default_times = %{
      "A" => 120.0,
      "B" => 125.0,
      "C" => 130.0,
      "D" => 135.0,
      "E" => 140.0,
      "F" => 145.0
    }

    updated_race =
      race
      |> Race.set_result!(%{
        positions: ranking,
        speeds: default_speeds,
        end_times: default_times,
        status: 2
      })

    # 广播比赛强制结束事件
    Phoenix.PubSub.broadcast(
      Cypridina.PubSub,
      Main.race_topic(),
      {:race_force_ended, updated_race}
    )

    {:ok, updated_race}
  end

  # 获取股票统计数据
  defp get_stock_statistics do
      # 获取所有动物的股票统计
      @animals
      |> Enum.map(fn animal ->
        case RacingGame.StockStatistics.get_by_racer(%{racer_id: animal.id}) do
          {:ok, stat} when not is_nil(stat) ->
            # 计算净持仓 = 总买入 - 总卖出
            net_position = (stat.total_bought || 0) - (stat.total_sold || 0)

            # 计算平均成本 = 总成本 / 净持仓
            average_cost = if net_position > 0 do
              Decimal.to_float(stat.total_cost || Decimal.new(0)) / net_position
            else
              0.0
            end

            {animal.id, %{
              total_bought: stat.total_bought || 0,
              total_sold: stat.total_sold || 0,
              total_cost: Decimal.to_float(stat.total_cost || Decimal.new(0)),
              total_revenue: Decimal.to_float(stat.total_revenue || Decimal.new(0)),
              net_position: net_position,
              average_cost: average_cost
            }}
          _ ->
            {animal.id, %{
              total_bought: 0,
              total_sold: 0,
              total_cost: 0.0,
              total_revenue: 0.0,
              net_position: 0,
              average_cost: 0.0
            }}
        end
      end)
      |> Enum.into(%{})
  end
end
