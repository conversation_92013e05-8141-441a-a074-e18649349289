defmodule Cypridina.Protocol.WebSocketHandler do
  @moduledoc """
  cypridina项目的WebSocket消息处理模块，替换IndiaGameServer和webproject
  实现IndiaGameClient中Protocol.ts定义的所有协议处理
  保持客户端协议枚举命名不变，以方便跨项目查找
  """

  require Logger
  # WebSocket 处理状态结构
  @type socket_state :: %{
          session_id: String.t(),
          user_id: String.t() | nil,
          connected_at: integer(),
          authenticate: boolean(),
          room_id: String.t() | nil,
          socket_info: map()
        }

  # 主协议常量 - 与IndiaGameClient/Protocol.ts的MainProto保持完全一致
  # RegLogin: 注册登录
  @main_proto_reg_login 0
  # FindPsw: 找回密码
  @main_proto_find_psw 1
  # Pay: 支付相关
  @main_proto_pay 2
  # IDRecord: 查询ID记录系统
  @main_proto_id_record 3
  # Game: 游戏逻辑
  @main_proto_game 4
  # XC: 子游戏服务器和客户端交互的协议
  @main_proto_xc 5
  # BaseInfo: 基本信息
  @main_proto_base_info 6
  # Money: 金钱钱包相关
  @main_proto_money 7
  # LevelExp: 等级经验相关
  @main_proto_level_exp 8
  # EMail: 邮箱
  @main_proto_email 9
  # Phone: 手机号
  @main_proto_phone 10
  # Cryptoguard: 密保相关
  @main_proto_cryptoguard 11
  # GameTime: 游戏时间
  @main_proto_game_time 12
  # MoneyRecord: 金钱钱包记录
  @main_proto_money_record 13
  # MailManager: 邮件系统
  @main_proto_mail_manager 14
  # NoticeManager: 公告系统
  @main_proto_notice_manager 15
  # CompetitionManager: 大奖赛
  @main_proto_competition_manager 16
  # TopPlayerManager: 大奖赛置顶玩家
  @main_proto_top_player_manager 17
  # Relief: 救济金
  @main_proto_relief 18
  # LoginRecord: 登陆记录
  @main_proto_login_record 19
  # OnlineReward: 在线奖励
  @main_proto_online_reward 20
  # DayLogin: 每日签到
  @main_proto_day_login 21
  # Lock: 锁机
  @main_proto_lock 22
  # CheckSystem: 验证系统
  @main_proto_check_system 23
  # ChengJiuManager: 成就
  @main_proto_chengjiu_manager 24
  # TaskManager: 任务
  @main_proto_task_manager 25
  # Present: 礼品
  @main_proto_present 26
  # PresentManager: 礼品管理
  @main_proto_present_manager 27
  # GameRecord: 游戏记录
  @main_proto_game_record 28
  # PresentNotice: 礼品领取公告
  @main_proto_present_notice 29
  # Vip: vip
  @main_proto_vip 30
  # PlayerTimer: 玩家定时器
  @main_proto_player_timer 31
  # Card: 实物卡
  @main_proto_card 32
  # AccountInfo: 账号信息
  @main_proto_account_info 33
  # DBServer: HallServer,GameServer,FCServer和DBServer通信
  @main_proto_db_server 34
  # ChatServer: 聊天服务器
  @main_proto_chat_server 35
  # LocalServer: 本地存储
  @main_proto_local_server 36
  # Rank: 排行榜
  @main_proto_rank 40
  # QMAgent: 全民代理 (弃用 主协议改为 DBServer: 34)
  @main_proto_qm_agent 41
  # Task: 活动任务
  @main_proto_task 42
  # MailManager: 邮件系统
  @main_proto_mail_manager 14
  # HallActivity: 大厅活动相关协议
  @main_proto_hall_activity 101

  # RegLogin 子协议常量 - 与IndiaGameClient/Protocol.ts的RegLogin保持完全一致
  # CS_NORMAL_REG_P: 普通注册
  @reg_login_cs_normal_reg_p 0
  # SC_NORMAL_REG_P
  @reg_login_sc_normal_reg_p 1
  # CS_LOGIN_P: 开始登陆
  @reg_login_cs_login_p 2
  # SC_LOGIN_P
  @reg_login_sc_login_p 3
  # CS_LOGIN_OUT_P: 登出
  @reg_login_cs_login_out_p 4
  # SC_ONLINE_P: 发送上线信息
  @reg_login_sc_online_p 5
  # CS_CHECK_REGINFO_P: 校验注册信息
  @reg_login_cs_check_reginfo_p 6
  # SC_CHECK_REGINFO_P: ["code"]:emNoramalReg
  @reg_login_sc_check_reginfo_p 7
  # CS_GET_RANDOM_NICKNAME_P: 客户端请求一个随机昵称
  @reg_login_cs_get_random_nickname_p 8
  # SC_GET_RANDOM_NICKNAME_P
  @reg_login_sc_get_random_nickname_p 9
  # SC_OHTER_LOGIN_P: 你的账号在别处登录,你已经被挤下线
  @reg_login_sc_other_login_p 10
  # SC_LOGIN_OTHER_P: 你的账号在别处登录,你把它挤下线
  @reg_login_sc_login_other_p 11
  # SC_SERVER_STOP_P: 服务器处于停机维护状态
  @reg_login_sc_server_stop_p 12
  # SC_UPDATE_SAVE_RANDOM_P: 更新保存密码随机数
  @reg_login_sc_update_save_random_p 13
  # SC_FULLCONNECT_ATTACK_P: 因为全连接攻击,你被断开连接
  @reg_login_sc_fullconnect_attack_p 14
  # SC_WEB_KILL_P: 你被踢下线
  @reg_login_sc_web_kill_p 15
  # CS_GAMESERVER_LOGIN_P: GameServer
  @reg_login_cs_gameserver_login_p 16
  # SC_GAMESERVER_LOGIN_P
  @reg_login_sc_gameserver_login_p 17
  # CS_HEART_CHECK_P: 心跳检查
  @reg_login_cs_heart_check_p 19
  # SC_HEART_CHECK_P
  @reg_login_sc_heart_check_p 20
  # CS_REQUEST_SERVER_VERSION_P: 请求版本号
  @reg_login_cs_request_server_version_p 38
  # SC_REQUEST_SERVER_VERSION_P: 返回版本号
  @reg_login_sc_request_server_version_p 39
  # CD_REQUEST_SYSTEM_STATUS_P: 请求系统状态配置
  @reg_login_cd_request_system_status_p 40
  # DC_REQUEST_SYSTEM_STATUS_P: 返回系统状态配置
  @reg_login_dc_request_system_status_p 41
  # CS_REQUEST_VERCODE_P: 请求验证码
  @reg_login_cs_request_vercode_p 41
  # SC_REQUEST_VERCODE_P
  @reg_login_sc_request_vercode_p 42
  # CS_RESPONSE_VERCODE_P: 响应验证码
  @reg_login_cs_response_vercode_p 43
  # SC_VERCODE_HALL_RESULT_P: 大厅验证码结果
  @reg_login_sc_vercode_hall_result_p 44
  # SC_VERCODE_GAME_RESULT_P: 游戏验证码结果
  @reg_login_sc_vercode_game_result_p 45
  # CD_REQUEST_SYSTEM_STATUS_P: 请求系统配置
  @reg_login_cd_request_system_status_p 49
  # DC_REQUEST_SYSTEM_STATUS_P: 返回系统配置
  @reg_login_dc_request_system_status_p 50
  # CS_REQUEST_GAMEVERSIONS_P: 请求游戏版本号列表
  @reg_login_cs_request_gameversions_p 51
  # SC_REQUEST_GAMEVERSIONS_P: 服务器下发游戏版本号列表
  @reg_login_sc_request_gameversions_p 52

  # BaseInfo 子协议常量 - 与IndiaGameClient/Protocol.ts的BaseInfo保持完全一致
  # CS_SET_NICKNAME_P: 设置昵称
  @base_info_cs_set_nickname_p 0
  # SC_SET_NICKNAME_RESULT_P
  @base_info_sc_set_nickname_result_p 1
  # SC_SET_NICKNAME_P
  @base_info_sc_set_nickname_p 2
  # CS_SET_HEADID_P: 设置头像ID
  @base_info_cs_set_headid_p 3
  # CS_SET_CUSTOM_HEAD_P: 设置为使用自定义头像
  @base_info_cs_set_custom_head_p 4
  # CS_CHANGE_PSW_P: 修改密码
  @base_info_cs_change_psw_p 5
  # SC_CHANGE_PSW_RESULT_P
  @base_info_sc_change_psw_result_p 6
  # SC_SET_LOTTERY_P: 奖券改变
  @base_info_sc_set_lottery_p 7
  # CS_CHANGE_PSW_CHECK_P: 验证修改后密码有效性
  @base_info_cs_change_psw_check_p 8
  # SC_CHANGE_PSW_CHECK_P
  @base_info_sc_change_psw_check_p 9
  # CS_SET_SPECPHONE_P: 设置特殊手机号
  @base_info_cs_set_specphone_p 10
  # SC_SET_SPECPHONE_P
  @base_info_sc_set_specphone_p 11
  # CS_FRIEND_P: 朋友圈
  @base_info_cs_friend_p 12
  # SC_FRIEND_P
  @base_info_sc_friend_p 13
  # SC_SET_ROBOT_LEVEL_P
  @base_info_sc_set_robot_level_p 14
  # SC_CHANGE_LOTTERY_P: 变更奖券(变更量)
  @base_info_sc_change_lottery_p 15
  # CD_SET_SEX_P: 设置玩家的性别
  @base_info_cd_set_sex_p 16
  # DC_SET_SEX_P
  @base_info_dc_set_sex_p 17

  # FindPsw 子协议常量 - 与IndiaGameClient/Protocol.ts的FindPsw保持完全一致
  # CS_FINDPSW_P: 找回密码
  @find_psw_cs_findpsw_p 0
  # SC_FINDPSW_P
  @find_psw_sc_findpsw_p 1
  # CS_FINDPSW_REQUEST_CODE_P: 请求手机验证码
  @find_psw_cs_findpsw_request_code_p 2
  # SC_FINDPSW_REQUEST_CODE_RESULT_P
  @find_psw_sc_findpsw_request_code_result_p 3
  # CS_FINDPSW_CRYPT_P: 输入密保答案
  @find_psw_cs_findpsw_crypt_p 4
  # CS_FINDPSW_PHONECODE_P: 输入手机验证码答案
  @find_psw_cs_findpsw_phonecode_p 5
  # SC_FINDPSW_CKECK_P: 验证结果
  @find_psw_sc_findpsw_ckeck_p 6
  # CS_FINDPSW_SET_NEW_PSW_P: 验证结束,设置新密码
  @find_psw_cs_findpsw_set_new_psw_p 7
  # SC_FINDPSW_SET_NEW_PSW_RESULT_P
  @find_psw_sc_findpsw_set_new_psw_result_p 8

  # Game 子协议常量
  @game_sc_add_gamelist_p 0
  @game_sc_del_gamelist_p 1
  @game_sc_room_info_p 2
  @game_cs_room_set_player_state_p 3
  @game_sc_room_set_player_state_p 4
  @game_sc_room_set_state_p 5
  @game_cs_room_chat_p 6
  @game_sc_room_chat_p 7
  @game_sc_room_reset_coin_p 8
  @game_sc_room_zanli_success_p 9
  @game_cs_room_zanli_comback_p 10
  @game_sc_room_zanli_comback_success_p 11
  @game_sc_room_player_enter_p 12
  @game_sc_room_watch_enter_p 13
  @game_sc_room_player_quit_p 14
  @game_sc_room_watch_quit_p 15
  @game_sc_room_del_p 16
  @game_sc_room_prepare_timeout_p 17
  @game_sc_room_del_player_p 18
  @game_sc_room_del_watch_p 19
  @game_xs_register_p 20
  @game_xs_del_watch_p 21
  @game_xs_player_zanli_p 22
  @game_xs_player_result_p 23
  @game_xs_result_p 24
  @game_xs_reset_coin_p 25
  @game_sx_create_game_p 26
  @game_sx_add_watch_p 27
  @game_sx_del_watch_p 28
  @game_sx_reset_coin_p 29
  @game_sx_player_leave_p 30
  @game_sx_player_online_p 31
  @game_sx_player_zanli_comback_p 32
  @game_sx_quit_p 33
  # CS_GAME_PLAYER_NUM_P: 请求每个游戏玩家人数表
  @game_cs_game_player_num_p 34
  # SC_GAME_PLAYER_NUM_P: 返回每个游戏玩家人数表
  @game_sc_game_player_num_p 35

  # DBServer 子协议常量
  # CS_NEW_CHARGE_LIST_P: 请求未读充值消息
  @db_server_cs_new_charge_list_p 100
  # SC_NEW_CHARGE_LIST_P: 返回未读充值消息
  @db_server_sc_new_charge_list_p 101

  # MailManager 子协议常量
  # CS_REQUEST_NEW_MAIL_COUNT_P: 请求新邮件数量
  @mail_manager_cs_request_new_mail_count_p 7
  # SC_REQUEST_NEW_MAIL_COUNT_P: 返回新邮件数量
  @mail_manager_sc_request_new_mail_count_p 8

  # Task 子协议常量
  # CS_GET_TODAY_MATCH_LIST: 获取今日比赛列表信息
  @task_cs_get_today_match_list 15
  # SC_GET_TODAY_MATCH_LIST: 获取今日比赛列表信息
  @task_sc_get_today_match_list 16

  # QMAgent 子协议常量 (通过DBServer主协议)
  # CD_GET_AGENT_CONFIG_P: 获取代理配置
  @qm_agent_cd_get_agent_config_p 3000
  # DC_GET_AGENT_CONFIG_P: 返回代理配置
  @qm_agent_dc_get_agent_config_p 3001
  @game_cs_quit_p 40
  @game_cs_huanzhuo_p 43
  @game_sc_huanzhuo_p 44
  @game_cs_mode1_enter_p 45
  @game_cs_mode1_robot_enter_p 46
  @game_sc_mode1_enter_p 47
  @game_cs_mode1_enter_pipei_p 93
  @game_sc_mode1_enter_pipei_p 94
  @game_sc_mode1_pipei_over_p 95
  @game_sc_mode1_quit_pipei_success_p 96
  @game_cs_room_player_ready 1102

  # XC 子协议常量
  @xc_room_info_p 0
  @xc_jiesuan_p 1

  # Money 子协议常量 - 与IndiaGameClient/Protocol.ts的Money保持完全一致
  # SC_SET_MONEY_P: 金钱改变
  @money_sc_set_money_p 0
  # SC_SET_WALLETMONEY_P: 钱包改变
  @money_sc_set_walletmoney_p 1
  # CS_SAVE_MONEY_P: 存钱
  @money_cs_save_money_p 2
  # SC_SAVE_MONEY_RESULT_P
  @money_sc_save_money_result_p 3
  # CS_GET_MONEY_P: 取钱
  @money_cs_get_money_p 4
  # SC_GET_MONEY_RESULT_P
  @money_sc_get_money_result_p 5
  # CS_TEST_ADD_MONEY_P: 测试,加钱
  @money_cs_test_add_money_p 6
  # SC_SET_GAME_MONEY_P: 金钱改变
  @money_sc_set_game_money_p 7
  # SC_SET_GAME_WALLETMONEY_P: 钱包改变
  @money_sc_set_game_walletmoney_p 8
  # SC_SET_HONOR_VALUE_P: 荣誉点改变
  @money_sc_set_honor_value_p 9
  # SC_SET_DIAMONDS_VALUE_P: 钻石改变
  @money_sc_set_diamonds_value_p 10
  # CS_DIAMONDS_CHANGE_MONEY_P: 钻石 兑换金币
  @money_cs_diamonds_change_money_p 11
  # SC_DIAMONDS_CHANGE_MONEY_P
  @money_sc_diamonds_change_money_p 12
  # CS_DIAMONDS_CHANGE_VIP_P: 钻石 兑换会员
  @money_cs_diamonds_change_vip_p 13
  # SC_DIAMONDS_CHANGE_VIP_P
  @money_sc_diamonds_change_vip_p 14
  # CS_DIAMONDS_TRANS_MONEY_CONFING_P: 钻石兑换金币配置
  @money_cs_diamonds_trans_money_confing_p 15
  # SC_DIAMONDS_TRANS_MONEY_CONFING_P
  @money_sc_diamonds_trans_money_confing_p 16
  # CS_DIAMONDS_TRANS_VIP_CONFING_P: 钻石兑换会员配置
  @money_cs_diamonds_trans_vip_confing_p 17
  # SC_DIAMONDS_TRANS_VIP_CONFING_P
  @money_sc_diamonds_trans_vip_confing_p 18
  # CS_REQUEST_ALMS_P: 查询救济金
  @money_cs_request_alms_p 37
  # SC_REQUEST_ALMS_RESULT_P: 查询救济金结果
  @money_sc_request_alms_result_p 38
  # CS_GET_ALMS_P: 领取救济金
  @money_cs_get_alms_p 35
  # SC_GET_ALMS_RESULT_P: 领取救济金结果
  @money_sc_get_alms_result_p 36
  # CS_AGENT_COMPLAINT_P: 投诉代理
  @money_cs_agent_complaint_p 46
  # CS_SERVICE_COMPLAINT_P: 投诉客服
  @money_cs_service_complaint_p 47
  # SC_COMPLAINT_RESULT_P: 投诉结果
  @money_sc_complaint_result_p 48
  # CS_PAY: 请求支付地址
  @money_cs_pay 49
  # SC_PAY: 请求支付返回
  @money_sc_pay 50
  # CS_VIP_PAY_LIST_P: 请求VIP支付列表
  @money_cs_vip_pay_list_p 51
  # SC_VIP_PAY_LIST_P: 请求VIP支付列表返回
  @money_sc_vip_pay_list_p 52

  # NoticeManager 子协议常量 - 与IndiaGameClient/Protocol.ts的NoticeManager保持完全一致
  # SC_NOTICE_P
  @notice_manager_sc_notice_p 0
  # CS_SEND_NOTICE_P: 请求发送公告
  @notice_manager_cs_send_notice_p 1
  # SC_SEND_NOTICE_P
  @notice_manager_sc_send_notice_p 2
  # CS_REQUEST_NOTICE_NEED_P: 请求发送公告所需
  @notice_manager_cs_request_notice_need_p 3
  # SC_REQUEST_NOTICE_NEED_P
  @notice_manager_sc_request_notice_need_p 4
  # CD_REQUEST_SYSTEM_NOTICE_P: 请求系统公告内容
  @notice_manager_cd_request_system_notice_p 5
  # DC_REQUEST_SYSTEM_NOTICE_P
  @notice_manager_dc_request_system_notice_p 6

  # MailManager 子协议常量 - 与IndiaGameClient/Protocol.ts的MailManager保持完全一致
  # CS_REQUEST_MAIL_INFO_P: 请求一封邮件的内容
  @mail_manager_cs_request_mail_info_p 0
  # SC_REQUEST_MAIL_INFO_P
  @mail_manager_sc_request_mail_info_p 1
  # CS_MAIL_SET_READ_P: 请求将一封邮件设置为已读
  @mail_manager_cs_mail_set_read_p 2
  # CS_DEL_MAIL_INFO_P: 请求删除一封邮件
  @mail_manager_cs_del_mail_info_p 3
  # SC_ADD_MAIL_P: 添加一封邮件
  @mail_manager_sc_add_mail_p 4
  # CS_REQUEST_MAILLIST_P: 请求邮件列表
  @mail_manager_cs_request_maillist_p 5
  # SC_REQUEST_MAILLIST_P
  @mail_manager_sc_request_maillist_p 6
  # CS_REQUEST_NEW_MAIL_COUNT_P
  @mail_manager_cs_request_new_mail_count_p 7
  # SC_REQUEST_NEW_MAIL_COUNT_P
  @mail_manager_sc_request_new_mail_count_p 8

  # Rank 子协议常量 - 与IndiaGameClient/Protocol.ts的Rank保持完全一致
  # CS_RANK_DATA: 获得排行榜信息
  @rank_cs_rank_data 0
  # SC_RANK_DATA: 后端返回排行榜信息
  @rank_sc_rank_data 1
  # CD_RANK_LIST: 向dbserver获得排行榜信息
  @rank_cd_rank_list 2
  # DC_RANK_LIST: dbserver返回排行榜信息
  @rank_dc_rank_list 3
  # CS_SELF_RANK_DATA_P: 自己的 今日金币排行榜
  @rank_cs_self_rank_data_p 4
  # SC_SELF_RANK_DATA_P
  @rank_sc_self_rank_data_p 5
  # CD_SELF_RANK_DATA
  @rank_cd_self_rank_data 6
  # DC_SELF_RANK_DATA
  @rank_dc_self_rank_data 7

  # QMAgent 子协议常量 - 与IndiaGameClient/Protocol.ts的QMAgent保持完全一致
  # CS_AGENT_PROMOTIONDATA: 获得推广佣金信息
  @qm_agent_cs_agent_promotiondata 0
  # SC_AGENT_PROMOTIONDATA: 返回获得推广佣金信息
  @qm_agent_sc_agent_promotiondata 1
  # CS_AGENT_GETMONEY: 领取佣金
  @qm_agent_cs_agent_getmoney 2
  # SC_AGENT_GETMONEY: 领取佣金
  @qm_agent_sc_agent_getmoney 3
  # CS_AGENT_MONEYDETAIL: 佣金明细
  @qm_agent_cs_agent_moneydetail 4
  # SC_AGENT_MONEYDETAIL: 佣金明细
  @qm_agent_sc_agent_moneydetail 5
  # CS_AGENT_MYTEAM: 我的团队
  @qm_agent_cs_agent_myteam 6
  # SC_AGENT_MYTEAM: 我的团队
  @qm_agent_sc_agent_myteam 7
  # CD_GET_AGENT_CONFIG_P: 获取代理配置
  @qm_agent_cd_get_agent_config_p 3000
  # DC_GET_AGENT_CONFIG_P: 返回代理配置
  @qm_agent_dc_get_agent_config_p 3001
  # CD_GET_AGENT_DIRECT_TOTAL_P: 自己的代理信息
  @qm_agent_cd_get_agent_direct_total_p 3002
  # DC_GET_AGENT_DIRECT_TOTAL_P: 返回代理信息
  @qm_agent_dc_get_agent_direct_total_p 3003
  # CD_GET_AGENT_DIRECT_DETAIL_P: 自己的收益明细
  @qm_agent_cd_get_agent_direct_detail_p 3004
  # DC_GET_AGENT_DIRECT_DETAIL_P: 返回收益明细
  @qm_agent_dc_get_agent_direct_detail_p 3005
  # CD_GET_AGENT_LIST_P: 获取下级列表
  @qm_agent_cd_get_agent_list_p 3006
  # DC_GET_AGENT_LIST_P: 返回下级列表
  @qm_agent_dc_get_agent_list_p 3007
  # CD_GET_AGENT_RANK_P: 代理收益排行榜
  @qm_agent_cd_get_agent_rank_p 3008
  # DC_GET_AGENT_RANK_P: 返回代理收益排行榜
  @qm_agent_dc_get_agent_rank_p 3009
  # CD_GET_AGENT_EXCHANGE_P: 代理兑换
  @qm_agent_cd_get_agent_exchange_p 3010
  # DC_GET_AGENT_EXCHANGE_P: 返回兑换消息
  @qm_agent_dc_get_agent_exchange_p 3011
  # CD_BAND_AGENT_RAND_CRAD_P: 代理绑定银行卡或者修改银行卡
  @qm_agent_cd_band_agent_rand_crad_p 3012
  # DC_BAND_AGENT_RAND_CRAD_P: 返回绑定银行卡
  @qm_agent_dc_band_agent_rand_crad_p 3013

  # Task 子协议常量 - 与IndiaGameClient/Protocol.ts的Task保持完全一致
  # CS_UPDATE_TASK_LIST: 更新任务列表信息
  @task_cs_update_task_list 0
  # SC_UPDATE_TASK_LIST: 更新任务列表信息
  @task_sc_update_task_list 1
  # CS_GET_TASK_REWARD: 领取任务奖励
  @task_cs_get_task_reward 2
  # SC_GET_TASK_REWARD: 领取任务奖励返回
  @task_sc_get_task_reward 3
  # SC_UPDATE_MATCH_ATTR: 更新比赛状态
  @task_sc_update_match_attr 10
  # CS_GET_MATCH_LIST: 获取比赛列表信息
  @task_cs_get_match_list 11
  # SC_GET_MATCH_LIST: 获取比赛列表信息
  @task_sc_get_match_list 12
  # CS_GET_MATCH_RANK_REWARD: 获取比赛排名和奖励
  @task_cs_get_match_rank_reward 13
  # SC_GET_MATCH_RANK_REWARD: 获取比赛排名和奖励返回
  @task_sc_get_match_rank_reward 14
  # CS_GET_TODAY_MATCH_LIST: 获取今日比赛列表信息
  @task_cs_get_today_match_list 15
  # SC_GET_TODAY_MATCH_LIST: 获取今日比赛列表信息
  @task_sc_get_today_match_list 16

  # HallActivity 子协议常量 - 与IndiaGameClient/Protocol.ts的HallActivity保持完全一致
  # CS_LOGINCASH_INFO_P: 请求登录活动信息
  @hall_activity_cs_logincash_info_p 0
  # SC_LOGINCASH_INFO_P: 请求登录活动信息返回
  @hall_activity_sc_logincash_info_p 1
  # CS_FETCH_LOGINCASH_AWARD_P: 领取登录活动奖励
  @hall_activity_cs_fetch_logincash_award_p 2
  # SC_FETCH_LOGINCASH_AWARD_P: 领取登录活动奖励返回
  @hall_activity_sc_fetch_logincash_award_p 3
  # CS_GET_USER_MONEY_P: 获取用户金币信息
  @hall_activity_cs_get_user_money_p 4
  # SC_GET_USER_MONEY_P: 返回用户金币信息
  @hall_activity_sc_get_user_money_p 5
  # CS_FETCH_USER_BONUS_P: 领取用户积分信息
  @hall_activity_cs_fetch_user_bonus_p 6
  # SC_FETCH_USER_BONUS_P: 返回领取用户积分信息
  @hall_activity_sc_fetch_user_bonus_p 7
  # CS_GET_SEVEN_DAYS_P: 请求7日签到活动信息
  @hall_activity_cs_get_seven_days_p 8
  # SC_GET_SEVEN_DAYS_P: 返回7日签到活动信息
  @hall_activity_sc_get_seven_days_p 9

  # DbServer 子协议常量 - 与IndiaGameClient/Protocol.ts的DbServer保持完全一致
  # SC_SET_HEADID_P: 设置头像返回
  @db_server_sc_set_headid_p 43
  # SC_WEB_CHANGE_ATTRIB_P: web请求变更玩家的属性
  @db_server_sc_web_change_attrib_p 37
  # CS_CUSTSRV_REPLY_P: 获取客服消息数据
  @db_server_cs_custsrv_reply_p 110
  # SC_CUSTSRV_REPLY_P: 响应客服消息数据
  @db_server_sc_custsrv_reply_p 111
  # CS_NEW_CHARGE_LIST_P: 获取未读充值消息
  @db_server_cs_new_charge_list_p 112
  # SC_NEW_CHARGE_LIST_P: 获取未读充值消息
  @db_server_sc_new_charge_list_p 113
  # CS_CUSTSRV_REDAY_MESSAGE: 发送已读客服信息
  @db_server_cs_custsrv_reday_message 114
  # CD_QUERY_GIFT_PACK_P: 新的查询礼包信息
  @db_server_cd_query_gift_pack_p 192
  # DC_QUERY_GIFT_PACK_P: 返回查询结果
  @db_server_dc_query_gift_pack_p 193
  # CD_STATISTICS_CHANNEL_DATA: FBC统计数据
  @db_server_cd_statistics_channel_data 194

  @doc """
  处理收到的WebSocket消息，根据主协议ID和子协议ID路由到相应的处理函数

  ## 参数
    - message: 解码后的消息 %{main_id: main_id, sub_id: sub_id, data: data}
    - state: 当前WebSocket连接状态

  ## 返回
    - {:reply, response_map, new_state} 回复消息（map格式，由msgpack序列化器处理）
    - {:ok, new_state} 不回复消息
  """

  # ==================== FindPsw 协议处理 ====================

  # 处理找回密码相关请求，委托给ProtocolHandlers模块
  def handle_message(%{main_id: @main_proto_find_psw, sub_id: sub_id} = message, state)
      when sub_id in [0, 2, 4, 5, 7] do
    case sub_id do
      0 ->
        Cypridina.Protocol.ProtocolHandlers.handle_find_password(message, state)

      2 ->
        Cypridina.Protocol.ProtocolHandlers.handle_find_password_request_code(message, state)

      4 ->
        Cypridina.Protocol.ProtocolHandlers.handle_find_password_crypt(message, state)

      5 ->
        Cypridina.Protocol.ProtocolHandlers.handle_find_password_phone_code(message, state)

      7 ->
        Cypridina.Protocol.ProtocolHandlers.handle_find_password_set_new_password(message, state)
    end
  end

  # ==================== NoticeManager 协议处理 ====================

  # 处理公告管理相关请求，委托给ProtocolHandlers模块
  def handle_message(%{main_id: @main_proto_notice_manager, sub_id: sub_id} = message, state)
      when sub_id in [1, 3, 5] do
    case sub_id do
      1 -> Cypridina.Protocol.ProtocolHandlers.handle_send_notice(message, state)
      3 -> Cypridina.Protocol.ProtocolHandlers.handle_request_notice_need(message, state)
      5 -> Cypridina.Protocol.ProtocolHandlers.handle_request_system_notice(message, state)
    end
  end

  # ==================== MailManager 协议处理 ====================

  # 处理邮件管理相关请求，委托给ProtocolHandlers模块
  def handle_message(%{main_id: @main_proto_mail_manager, sub_id: sub_id} = message, state)
      when sub_id in [0, 2, 3, 5, 7] do
    case sub_id do
      0 -> Cypridina.Protocol.ProtocolHandlers.handle_request_mail_info(message, state)
      2 -> Cypridina.Protocol.ProtocolHandlers.handle_mail_set_read(message, state)
      3 -> Cypridina.Protocol.ProtocolHandlers.handle_delete_mail(message, state)
      5 -> Cypridina.Protocol.ProtocolHandlers.handle_request_mail_list(message, state)
      7 -> Cypridina.Protocol.ProtocolHandlers.handle_request_new_mail_count(message, state)
    end
  end

  # ==================== Rank 协议处理 ====================

  # 处理排行榜相关请求，委托给ProtocolHandlers模块
  def handle_message(%{main_id: @main_proto_rank, sub_id: sub_id} = message, state)
      when sub_id in [0, 4] do
    case sub_id do
      0 -> Cypridina.Protocol.ProtocolHandlers.handle_rank_data(message, state)
      4 -> Cypridina.Protocol.ProtocolHandlers.handle_self_rank_data(message, state)
    end
  end

  # ==================== QMAgent 协议处理 ====================

  # 处理全民代理相关请求，委托给ExtendedHandlers模块
  def handle_message(%{main_id: @main_proto_qm_agent, sub_id: sub_id} = message, state)
      when sub_id in [0, 2, 4, 6] do
    case sub_id do
      0 -> Cypridina.Protocol.ExtendedHandlers.handle_agent_promotion_data(message, state)
      2 -> Cypridina.Protocol.ExtendedHandlers.handle_agent_get_money(message, state)
      4 -> Cypridina.Protocol.ExtendedHandlers.handle_agent_money_detail(message, state)
      6 -> Cypridina.Protocol.ExtendedHandlers.handle_agent_my_team(message, state)
    end
  end

  # ==================== Task 协议处理 ====================

  # 处理任务相关请求，委托给ExtendedHandlers模块
  def handle_message(%{main_id: @main_proto_task, sub_id: sub_id} = message, state)
      when sub_id in [0, 2, 11, 13] do
    case sub_id do
      0 -> Cypridina.Protocol.ExtendedHandlers.handle_update_task_list(message, state)
      2 -> Cypridina.Protocol.ExtendedHandlers.handle_get_task_reward(message, state)
      11 -> Cypridina.Protocol.ExtendedHandlers.handle_get_match_list(message, state)
      13 -> Cypridina.Protocol.ExtendedHandlers.handle_get_match_rank_reward(message, state)
    end
  end

  # ==================== HallActivity 协议处理 ====================

  # 处理大厅活动相关请求，委托给ExtendedHandlers模块
  def handle_message(%{main_id: @main_proto_hall_activity, sub_id: sub_id} = message, state)
      when sub_id in [0, 2, 4, 6, 8] do
    case sub_id do
      0 -> Cypridina.Protocol.ExtendedHandlers.handle_login_cash_info(message, state)
      2 -> Cypridina.Protocol.ExtendedHandlers.handle_fetch_login_cash_award(message, state)
      4 -> Cypridina.Protocol.ExtendedHandlers.handle_get_user_money(message, state)
      6 -> Cypridina.Protocol.ExtendedHandlers.handle_fetch_user_bonus(message, state)
      8 -> Cypridina.Protocol.ExtendedHandlers.handle_get_seven_days(message, state)
    end
  end


  # ==================== Game 协议处理 ====================

  # 处理匹配请求 (Game.CS_MODE1_ENTER_PIPEI_P)
  def handle_message(
        %{main_id: @main_proto_game, sub_id: @game_cs_mode1_enter_pipei_p} = message,
        state
      ) do
    Logger.info(
      "📥 [GAME] 玩家请求匹配 - MainID: #{@main_proto_game}, SubID: #{@game_cs_mode1_enter_pipei_p}, Data: #{inspect(message.data)}"
    )

    # 获取用户ID，如果没有则生成一个临时ID
    user_id = state.user_id || "temp_user_#{:rand.uniform(10000)}"

    # 解析匹配数据
    match_data = message.data || %{}
    # game_id從game_channel的状态里读取
    game_id = state.game_id

    # 调用房间管理器进行匹配
    case Cypridina.RoomSystem.RoomManager.match_room(user_id, game_id, match_data) do
      {:ok, room_info} ->
        Logger.info("🎯 [MATCH] 匹配成功: 用户 #{user_id} -> 房间 #{room_info.room_id}")

        # 更新状态中的房间信息
        new_state = %{state | room_id: room_info.room_id}

        response_data = %{
          # 0表示成功
          "status" => 0,
          # 错误消息，成功时为空
          "msg" => "",
          # 成功消息
          "message" => "匹配成功",
          # 房间ID
          "room_id" => room_info.room_id,
          # 座位ID
          "seat_id" => room_info.seat_id,
          # 最大玩家数
          "max_players" => room_info.max_players,
          # 当前玩家数
          "current_players" => room_info.current_players,
          # 游戏类型
          "game_type" => game_id,
          # 房间类型 (匹配房间)
          "room_type" => 1,
          # 服务器时间
          "server_time" => System.system_time(:millisecond)
        }

        response_map = %{
          "mainId" => @main_proto_game,
          "subId" => @game_sc_mode1_enter_pipei_p,
          "data" => response_data
        }

        Logger.info(
          "📤 [GAME] 返回匹配响应 - MainID: #{@main_proto_game}, SubID: #{@game_sc_mode1_enter_pipei_p}, RoomID: #{room_info.room_id}"
        )

        {:reply, response_map, new_state}

      {:error, reason} ->
        Logger.error("🎯 [MATCH] 匹配失败: #{reason}")

        response_data = %{
          # 1表示失败
          "status" => 1,
          # 错误码
          "code" => 1001,
          # 错误消息
          "msg" => "匹配失败: #{reason}",
          "message" => "匹配失败",
          "room_id" => "",
          "seat_id" => 0,
          "max_players" => 0,
          "current_players" => 0,
          "game_type" => game_id,
          "room_type" => 0,
          "server_time" => System.system_time(:millisecond)
        }

        response_map = %{
          "mainId" => @main_proto_game,
          "subId" => @game_sc_mode1_enter_pipei_p,
          "data" => response_data
        }

        {:reply, response_map, state}
    end
  end

  # 处理注册请求 (RegLogin.CS_NORMAL_REG_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_normal_reg_p} = message,
        state
      ) do
    Logger.info("用户注册请求: #{inspect(message.data)}")

    data = message.data || %{}
    username = Map.get(data, "username", "")
    password = Map.get(data, "password", "")

    # 模拟注册验证
    response_data =
      cond do
        String.length(username) < 3 ->
          %{
            "status" => 1,
            "message" => "用户名长度不能少于3个字符"
          }

        String.length(password) < 6 ->
          %{
            "status" => 2,
            "message" => "密码长度不能少于6个字符"
          }

        true ->
          user_id = "user_#{:rand.uniform(100_000)}"

          %{
            "status" => 0,
            "message" => "注册成功",
            "userid" => user_id,
            "username" => username
          }
      end

    response_map = %{
      "mainId" => @main_proto_reg_login,
      "subId" => @reg_login_sc_normal_reg_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理登录请求 (RegLogin.CS_LOGIN_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_login_p} = message,
        state
      ) do
    Logger.info(
      "📥 [PROTOCOL] 用户登录请求 - MainID: #{@main_proto_reg_login}, SubID: #{@reg_login_cs_login_p}, Data: #{inspect(message.data)}"
    )

    data = message.data || %{}
    account = Map.get(data, "account", "")
    password = Map.get(data, "password", "")
    # 默认为快速登录
    accounttype = Map.get(data, "accounttype", 3)
    uniquecode = Map.get(data, "uniquecode", "")
    siteid = Map.get(data, "siteid", 1)
    promotionid = Map.get(data, "promotionid", 0)

    # 处理快速登录逻辑
    case accounttype do
      3 ->
        # 快速登录 - 通过 client_uniq_id 创建或获取游客账户
        handle_quick_login(uniquecode, message, state)

      _ ->
        # 普通登录 - 验证用户名密码
        handle_normal_login(account, password, message, state)
    end
  end

  # 处理快速登录
  defp handle_quick_login(client_uniq_id, message, state) when client_uniq_id != "" do
    Logger.info("🚀 [QUICK_LOGIN] 快速登录请求 - client_uniq_id: #{client_uniq_id}")

    case Cypridina.Protocol.QuickLoginHandler.handle_quick_login(client_uniq_id) do
      {:ok, user} ->
        Logger.info("🚀 [QUICK_LOGIN] 快速登录成功 - 用户ID: #{user.id}")
        build_login_success_response(user, 3, message, state)

      {:error, reason} ->
        Logger.error("🚀 [QUICK_LOGIN] 快速登录失败 - 原因: #{reason}")
        build_login_error_response("快速登录失败: #{reason}", message, state)
    end
  end

  defp handle_quick_login("", _message, state) do
    Logger.warning("🚀 [QUICK_LOGIN] 快速登录失败 - 缺少客户端唯一标识")
    build_login_error_response("缺少客户端唯一标识", nil, state)
  end

  # 处理普通登录
  defp handle_normal_login(account, password, message, state) do
    login_success = String.length(account) >= 3 and String.length(password) >= 6

    if login_success do
      # 模拟用户信息
      user = %{
        id: "user_#{:rand.uniform(100000)}",
        username: account,
        numeric_id: :rand.uniform(999999)
      }
      build_login_success_response(user, 1, message, state)
    else
      build_login_error_response("用户名或密码错误", message, state)
    end
  end

  # 构建登录成功响应
  defp build_login_success_response(user, accounttype, message, state) do

    user = user |> Ash.load!([:asset])
    # 从用户对象获取信息
    player_id = user.numeric_id
    user_id = user.id
    username = user.username
    {:ok, token, _claims} = AshAuthentication.Jwt.token_for_user(user)

    Logger.info("🚀 [LOGIN_SUCCESS] 登录成功 - 用户ID: #{user_id}, 用户名: #{username}, PlayerID: #{player_id}, user: #{inspect(user)}")

    # 构建完整的登录响应数据，符合客户端期望的格式
    response_data = %{
        # CONST_LOGIN_RESULT_SUCCESS
        "code" => 0,
        "msg" => "登录成功",
        "playerid" => player_id,
        "firstlogin" => 0,
        "random" => "#{:rand.uniform(999_999)}",
        "isgame" => 0,
        "serverport" => 0,

        # 用户功能数据 - 客户端期望的Function字段
        "Function" => %{
          # BaseInfo 相关数据
          "6" => %{
            "nickname" => username,
            "headid" => 1,
            "headframeid" => 1,
            "sex" => 1,
            # 初始金币
            "money" => 10000,
            # 保险柜金币
            "walletmoney" => 0,
            # 今日赢取金币
            "GameWinAmount" => 0,
            "AduitStatus" => 0,
            "CustomHeadUrl" => "",
            "wxheadurl" => ""
          },
          # Money 相关数据
          "7" => %{
            "BankName" => "",
            "BankAccountNum" => "",
            "BankAccountName" => "",
            "VipLevel" => 0,
            "VipExp" => 0
          }
        },

        # 登录参数 - 客户端期望的loginparam字段
        "loginparam" => %{
          "account" => if(accounttype == 3, do: "#{player_id}", else: username),
          # "userToken" => token,
          "password" => "",
          "accounttype" => accounttype,
          "openid" => "",
          "nickname" => "",
          "headimgurl" => "",
          "city" => "",
          "sex" => 1
        },

        # 游戏房间列表 - 提供完整的游戏房间数据
        "gamelist" => %{
          # TeenPatti房间
          "1" => %{
            "gameid" => 1,
            "serverid" => 1001,
            "port" => 4000,
            "ip" => "127.0.0.1",
            "orderid" => 1,
            "difen" => 10,
            "money" => 100,
            "dingfen" => 0,
            "gamemaxnum" => 6,
            "bundleName" => "teenpatti"
          },
          # TeenPatti房间2
          "2" => %{
            "gameid" => 1,
            "serverid" => 1002,
            "port" => 4000,
            "ip" => "127.0.0.1",
            "orderid" => 2,
            "difen" => 50,
            "money" => 500,
            "dingfen" => 0,
            "gamemaxnum" => 6,
            "bundleName" => "teenpatti"
          },
          # DragonTiger房间
          "3" => %{
            "gameid" => 22,
            "serverid" => 2201,
            "port" => 4000,
            "ip" => "127.0.0.1",
            "orderid" => 1,
            "difen" => 20,
            "money" => 200,
            "dingfen" => 0,
            "gamemaxnum" => 8,
            "bundleName" => "longhu"
          },
          # Slot777房间
          "4" => %{
            "gameid" => 40,
            "serverid" => 4001,
            "port" => 4000,
            "ip" => "127.0.0.1",
            "orderid" => 1,
            "difen" => 10,
            "money" => 10,
            "dingfen" => 0,
            "gamemaxnum" => 1,
            "bundleName" => "slot777"
          }
        },

        # 站点游戏列表 - 提供游戏配置信息
        "sitegamelist1" => %{
          "1" => %{
            "gameid" => 1,
            # 2-正常运行
            "status" => 2,
            "mode" => "",
            "name" => "teenpatti"
          },
          "2" => %{
            "gameid" => 22,
            # 2-正常运行
            "status" => 2,
            "mode" => "",
            "name" => "longhu"
          },
          "3" => %{
            "gameid" => 40,
            # 2-正常运行
            "status" => 2,
            "mode" => "",
            "name" => "slot777"
          }
        },

        # 其他配置信息
        "agentextips" => "",
        "bankextips" => "",
        "isbindaccount" => 0,
        "regsendmoney" => 0,
        "officalwebdisplay" => 1,
        "IsShowCode" => 0,
        "promotion" => 0,
        "styleid" => 1,
        "Url" => ""
      }

    # 更新会话状态
    new_state =
      Map.merge(state, %{
        user_id: user_id,
        player_id: player_id,
        account: username,
        accounttype: accounttype,
        authenticate: true,
        current_user: user
      })

    response_map = %{
      "mainId" => @main_proto_reg_login,
      "subId" => @reg_login_sc_login_p,
      "data" => response_data
    }

    {:reply, response_map, new_state}
  end

  # 构建登录错误响应
  defp build_login_error_response(error_msg, _message, state) do
    response_data = %{
      # CONST_LOGIN_RESULT_PSW_ERROR
      "code" => 4,
      "msg" => error_msg
    }

    response_map = %{
      "mainId" => @main_proto_reg_login,
      "subId" => @reg_login_sc_login_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理心跳请求 (RegLogin.CS_HEART_CHECK_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_heart_check_p} = _message,
        state
      ) do
    Logger.debug("收到心跳请求: #{inspect(state.user_id)}")

    # 回复心跳响应
    response_data = %{
      "server_time" => System.system_time(:millisecond)
    }

    response_map = %{
      "mainId" => @main_proto_reg_login,
      "subId" => @reg_login_sc_heart_check_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理请求服务器版本号 (RegLogin.CS_REQUEST_SERVER_VERSION_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_request_server_version_p} =
          message,
        state
      ) do
    Logger.info("收到请求服务器版本号: #{inspect(message.data)}")

    # 返回服务器版本信息
    response_data = %{
      # 大厅版本号
      "hall_version" => "1.0.0",
      # 游戏版本号
      "game_version" => "1.0.0",
      # 服务器名称
      "server_name" => "cypridina",
      # 构建时间
      "build_time" => "2024-01-01",
      # 成功状态
      "status" => 0
    }

    response_map = %{
      "mainId" => @main_proto_reg_login,
      "subId" => @reg_login_sc_request_server_version_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理请求系统状态配置 (RegLogin.CD_REQUEST_SYSTEM_STATUS_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cd_request_system_status_p} =
          message,
        state
      ) do
    Logger.info("收到请求系统状态配置: #{inspect(message.data)}")

    data = message.data || %{}
    siteid = Map.get(data, "siteid", 1)
    userid = Map.get(data, "userid", 0)

    # 返回系统配置信息
    response_data = %{
      "status" => 0,
      "siteid" => siteid,
      "userid" => userid,
      "server_time" => System.system_time(:millisecond),
      "config" => %{
        # 是否维护中
        "maintenance" => false,
        # 是否需要版本检查
        "version_check" => false,
        # 是否强制更新
        "force_update" => false,
        "notice" => "欢迎来到cypridina游戏服务器！",
        # 最低支持版本
        "min_version" => "1.0.0",
        # 下载地址
        "download_url" => "",
        "customer_service" => "在线客服",
        "announcement" => "系统运行正常"
      },
      "message" => "获取系统配置成功"
    }

    response_map = %{
      "mainId" => @main_proto_reg_login,
      "subId" => @reg_login_dc_request_system_status_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理登出请求 (RegLogin.CS_LOGIN_OUT_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_login_out_p} = message,
        state
      ) do
    Logger.info("用户登出请求: #{inspect(message.data)}")

    # 清理用户状态
    new_state = %{state | user_id: nil, authenticate: false, room_id: nil}

    # 回复登出成功（客户端期望SC_ONLINE_P响应）
    response_data = %{
      "status" => 0,
      "message" => "登出成功"
    }

    response_map = %{
      "mainId" => @main_proto_reg_login,
      "subId" => @reg_login_sc_online_p,
      "data" => response_data
    }

    {:reply, response_map, new_state}
  end

  # 处理检查注册信息请求 (RegLogin.CS_CHECK_REGINFO_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_check_reginfo_p} = message,
        state
      ) do
    Logger.info("检查注册信息请求: #{inspect(message.data)}")

    data = message.data || %{}
    username = Map.get(data, "username", "")

    # 模拟检查用户名是否已存在
    # 30%概率用户名已存在
    exists = :rand.uniform(10) > 7

    response_data = %{
      "status" => if(exists, do: 1, else: 0),
      "message" => if(exists, do: "用户名已存在", else: "用户名可用"),
      "username" => username
    }

    response_map = %{
      "mainId" => @main_proto_reg_login,
      "subId" => @reg_login_sc_check_reginfo_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理获取随机昵称请求 (RegLogin.CS_GET_RANDOM_NICKNAME_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_get_random_nickname_p} = message,
        state
      ) do
    Logger.info("获取随机昵称请求: #{inspect(message.data)}")

    # 随机昵称库
    adjectives = ["快乐的", "勇敢的", "聪明的", "幸运的", "神秘的", "闪耀的", "强大的", "优雅的"]
    nouns = ["玩家", "勇士", "法师", "射手", "骑士", "刺客", "守护者", "探险家"]

    random_nickname = Enum.random(adjectives) <> Enum.random(nouns) <> "#{:rand.uniform(999)}"

    response_data = %{
      "status" => 0,
      "nickname" => random_nickname
    }

    response_map = %{
      "mainId" => @main_proto_reg_login,
      "subId" => @reg_login_sc_get_random_nickname_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理请求验证码 (RegLogin.CS_REQUEST_VERCODE_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_request_vercode_p} = message,
        state
      ) do
    Cypridina.Protocol.SystemHandlers.handle_request_vercode(message, state)
  end

  # 处理响应验证码 (RegLogin.CS_RESPONSE_VERCODE_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_response_vercode_p} = message,
        state
      ) do
    Cypridina.Protocol.SystemHandlers.handle_response_vercode(message, state)
  end

  # 处理请求系统配置 (RegLogin.CD_REQUEST_SYSTEM_STATUS_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cd_request_system_status_p} =
          message,
        state
      ) do
    Cypridina.Protocol.SystemHandlers.handle_request_system_status(message, state)
  end

  # 处理请求游戏版本号列表 (RegLogin.CS_REQUEST_GAMEVERSIONS_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_request_gameversions_p} = message,
        state
      ) do
    Cypridina.Protocol.SystemHandlers.handle_request_game_versions(message, state)
  end

  # 处理游戏服务器登录 (RegLogin.CS_GAMESERVER_LOGIN_P)
  def handle_message(
        %{main_id: @main_proto_reg_login, sub_id: @reg_login_cs_gameserver_login_p} = message,
        state
      ) do
    Cypridina.Protocol.SystemHandlers.handle_gameserver_login(message, state)
  end

  # ==================== BaseInfo 协议处理 ====================

  # 处理设置昵称请求 (BaseInfo.CS_SET_NICKNAME_P)
  def handle_message(
        %{main_id: @main_proto_base_info, sub_id: @base_info_cs_set_nickname_p} = message,
        state
      ) do
    Logger.info("设置昵称请求: #{inspect(message.data)}")

    data = message.data || %{}
    nickname = Map.get(data, "nickname", "")

    # 验证昵称
    response_data =
      cond do
        String.length(nickname) < 2 ->
          %{
            "status" => 1,
            "message" => "昵称长度不能少于2个字符"
          }

        String.length(nickname) > 20 ->
          %{
            "status" => 2,
            "message" => "昵称长度不能超过20个字符"
          }

        true ->
          %{
            "status" => 0,
            "message" => "昵称设置成功",
            "nickname" => nickname,
            "user_id" => state.user_id
          }
      end

    response_map = %{
      "mainId" => @main_proto_base_info,
      "subId" => @base_info_sc_set_nickname_result_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理设置头像ID请求 (BaseInfo.CS_SET_HEADID_P)
  def handle_message(
        %{main_id: @main_proto_base_info, sub_id: @base_info_cs_set_headid_p} = message,
        state
      ) do
    Logger.info("设置头像ID请求: #{inspect(message.data)}")

    data = message.data || %{}
    head_id = Map.get(data, "head_id", 1)

    response_data = %{
      "status" => 0,
      "message" => "头像设置成功",
      "head_id" => head_id,
      "user_id" => state.user_id
    }

    response_map = %{
      "mainId" => @main_proto_base_info,
      "subId" => @base_info_cs_set_headid_p + 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理设置自定义头像请求 (BaseInfo.CS_SET_CUSTOM_HEAD_P)
  def handle_message(
        %{main_id: @main_proto_base_info, sub_id: @base_info_cs_set_custom_head_p} = message,
        state
      ) do
    Logger.info("设置自定义头像请求: #{inspect(message.data)}")

    data = message.data || %{}
    head_url = Map.get(data, "head_url", "")

    response_data = %{
      "status" => 0,
      "message" => "自定义头像设置成功",
      "head_url" => head_url,
      "user_id" => state.user_id
    }

    response_map = %{
      "mainId" => @main_proto_base_info,
      "subId" => @base_info_cs_set_custom_head_p + 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理修改密码请求 (BaseInfo.CS_CHANGE_PSW_P)
  def handle_message(
        %{main_id: @main_proto_base_info, sub_id: @base_info_cs_change_psw_p} = message,
        state
      ) do
    Logger.info("修改密码请求: #{inspect(message.data)}")

    data = message.data || %{}
    old_password = Map.get(data, "old_password", "")
    new_password = Map.get(data, "new_password", "")

    # 验证密码
    response_data =
      cond do
        String.length(old_password) == 0 ->
          %{
            "status" => 1,
            "message" => "请输入原密码"
          }

        String.length(new_password) < 6 ->
          %{
            "status" => 2,
            "message" => "新密码长度不能少于6个字符"
          }

        true ->
          %{
            "status" => 0,
            "message" => "密码修改成功",
            "user_id" => state.user_id
          }
      end

    response_map = %{
      "mainId" => @main_proto_base_info,
      "subId" => @base_info_sc_change_psw_result_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理设置性别请求 (BaseInfo.CD_SET_SEX_P)
  def handle_message(
        %{main_id: @main_proto_base_info, sub_id: @base_info_cd_set_sex_p} = message,
        state
      ) do
    Logger.info("设置性别请求: #{inspect(message.data)}")

    data = message.data || %{}
    # 1-男, 2-女
    sex = Map.get(data, "sex", 1)

    response_data = %{
      "status" => 0,
      "message" => "性别设置成功",
      "sex" => sex,
      "user_id" => state.user_id
    }

    response_map = %{
      "mainId" => @main_proto_base_info,
      "subId" => @base_info_dc_set_sex_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理BaseInfo协议的其他请求
  def handle_message(%{main_id: @main_proto_base_info, sub_id: sub_id} = message, state) do
    Logger.info("BaseInfo协议消息: [#{message.main_id}][#{sub_id}] - #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      "message" => "base_info_received",
      "original_sub_id" => sub_id
    }

    response_map = %{
      "mainId" => @main_proto_base_info,
      "subId" => sub_id + 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理游戏准备消息
  def handle_message(
        %{main_id: @main_proto_game, sub_id: @game_cs_room_player_ready} = message,
        state
      ) do
    Logger.info("玩家准备游戏: #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      "message" => "ready_success"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_cs_room_player_ready + 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理匹配请求
  def handle_message(
        %{main_id: @main_proto_game, sub_id: @game_cs_mode1_enter_pipei_p} = message,
        state
      ) do
    Logger.info("🎮 [PIPEI] 玩家请求匹配: #{inspect(message.data)}")

    # 模拟匹配成功，进入游戏
    response_data = %{
      # 客户端期望的字段
      "code" => 0,
      # 错误消息（成功时为空）
      "msg" => "",
      "status" => 0,
      "room_id" => "room_#{:rand.uniform(1000)}",
      "seat_id" => 1,
      "max_players" => 6,
      "message" => "匹配成功"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      # 注意：这里应该是匹配响应协议
      "subId" => @game_sc_mode1_enter_pipei_p,
      "data" => response_data
    }

    Logger.info("🎮 [PIPEI] 匹配成功 - RoomID: #{response_data["room_id"]}")

    {:reply, response_map, state}
  end

  # 处理退出请求
  def handle_message(%{main_id: @main_proto_game, sub_id: @game_cs_quit_p} = message, state) do
    Logger.info("🚪 [QUIT] 玩家请求退出: #{inspect(message.data)}")

    # 如果玩家在房间中，从房间中移除
    if state.room_id do
      Logger.info("🚪 [QUIT] 玩家从房间 #{state.room_id} 退出")
      # 这里可以添加从房间管理器中移除玩家的逻辑
    end

    # 清理玩家状态
    new_state = %{
      state
      | room_id: nil,
        seat_id: nil,
        game_room_entered: false
    }

    # 根据原始IndiaGameServer的实现，退出请求通常不需要特定的响应
    # 客户端发送退出请求后会直接关闭连接或返回大厅
    # 我们返回一个简单的确认，但不使用错误的协议ID
    Logger.info("🚪 [QUIT] 玩家退出处理完成")

    # 不发送响应，让客户端自行处理退出逻辑
    {:ok, new_state}
  end

  # 处理聊天消息
  def handle_message(%{main_id: @main_proto_game, sub_id: @game_cs_room_chat_p} = message, state) do
    Logger.info("玩家聊天: #{inspect(message.data)}")

    # 广播聊天消息给房间内其他玩家
    response_data =
      Map.merge(message.data || %{}, %{
        "user_id" => state.user_id,
        "timestamp" => System.system_time(:millisecond)
      })

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_cs_room_chat_p + 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理房间设置玩家状态请求
  def handle_message(
        %{main_id: @main_proto_game, sub_id: @game_cs_room_set_player_state_p} = message,
        state
      ) do
    Logger.info("设置玩家状态: #{inspect(message.data)}")

    data = message.data || %{}
    player_state = Map.get(data, "state", 0)

    response_data = %{
      "status" => 0,
      "user_id" => state.user_id,
      "player_state" => player_state,
      "message" => "状态设置成功"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_sc_room_set_player_state_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理换桌请求
  def handle_message(%{main_id: @main_proto_game, sub_id: @game_cs_huanzhuo_p} = message, state) do
    Logger.info("玩家请求换桌: #{inspect(message.data)}")

    # 模拟换桌成功
    new_room_id = "room_#{:rand.uniform(1000)}"
    new_state = %{state | room_id: new_room_id}

    response_data = %{
      "status" => 0,
      "room_id" => new_room_id,
      "seat_id" => :rand.uniform(6),
      "message" => "换桌成功"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_sc_huanzhuo_p,
      "data" => response_data
    }

    {:reply, response_map, new_state}
  end

  # 处理断线重连请求
  def handle_message(
        %{main_id: @main_proto_game, sub_id: @game_cs_room_zanli_comback_p} = message,
        state
      ) do
    Logger.info("玩家断线重连: #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      "room_id" => state.room_id || "room_default",
      "user_id" => state.user_id,
      "message" => "重连成功"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_cs_room_zanli_comback_p + 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理模式1进入请求
  def handle_message(
        %{main_id: @main_proto_game, sub_id: @game_cs_mode1_enter_p} = message,
        state
      ) do
    Logger.info("🎮 [MODE1_ENTER] 玩家请求进入模式1: #{inspect(message.data)}")

    data = message.data || %{}
    game_type = Map.get(data, "game_type", 1)

    # 从Channel上下文获取游戏房间信息
    {server_id, game_id} =
      case Map.get(state, :channel_type) do
        :game_room ->
          channel_info = Map.get(state, :channel_info, %{})
          {Map.get(channel_info, :server_id, 0), Map.get(channel_info, :game_id, 0)}

        _ ->
          {0, 0}
      end

    # 客户端期望的响应格式：result.code == 0 表示成功
    response_data = %{
      # 客户端检查的主要字段
      "code" => 0,
      # 错误消息（成功时为空）
      "msg" => "",
      # 兼容字段
      "status" => 0,
      "game_type" => game_type,
      "game_id" => game_id,
      "server_id" => server_id,
      "room_id" => "mode1_room_#{game_id}_#{server_id}",
      "max_players" => 6,
      "current_players" => 1,
      # 分配的座位号
      "seat_id" => 1,
      "user_id" => state.user_id,
      "message" => "进入游戏成功"
    }

    Logger.info(
      "🎮 [MODE1_ENTER] 进入成功 - GameID: #{game_id}, ServerID: #{server_id}, UserID: #{state.user_id}"
    )

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_sc_mode1_enter_p,
      "data" => response_data
    }

    # 更新状态，记录房间信息
    new_state =
      Map.merge(state, %{
        game_room_entered: true,
        room_id: response_data["room_id"],
        seat_id: response_data["seat_id"]
      })

    {:reply, response_map, new_state}
  end

  # 处理机器人进入请求
  def handle_message(
        %{main_id: @main_proto_game, sub_id: @game_cs_mode1_robot_enter_p} = message,
        state
      ) do
    Logger.info("机器人进入请求: #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      "robot_count" => :rand.uniform(3),
      "message" => "机器人已加入"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_sc_mode1_enter_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理房间信息请求 (Game.SC_ROOM_INFO_P)
  def handle_message(%{main_id: @main_proto_game, sub_id: @game_sc_room_info_p} = message, state) do
    Logger.info("房间信息请求: #{inspect(message.data)}")

    # 模拟房间信息
    room_info = %{
      "room_id" => state.room_id || "room_default",
      "room_name" => "经典房间",
      "game_type" => 1,
      "max_players" => 6,
      "current_players" => 3,
      "min_money" => 1000,
      # 0-等待, 1-游戏中
      "game_status" => 0,
      "round" => 1,
      "players" => [
        %{
          "user_id" => state.user_id,
          "nickname" => "玩家1",
          "money" => 10000,
          "seat_id" => 1,
          # 1-准备, 0-未准备
          "status" => 1
        },
        %{
          "user_id" => "user_2",
          "nickname" => "玩家2",
          "money" => 8000,
          "seat_id" => 2,
          "status" => 1
        },
        %{
          "user_id" => "user_3",
          "nickname" => "玩家3",
          "money" => 12000,
          "seat_id" => 3,
          "status" => 0
        }
      ]
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_sc_room_info_p,
      "data" => room_info
    }

    {:reply, response_map, state}
  end

  # 处理玩家进入房间 (Game.SC_ROOM_PLAYER_ENTER_P)
  def handle_message(
        %{main_id: @main_proto_game, sub_id: @game_sc_room_player_enter_p} = message,
        state
      ) do
    Logger.info("玩家进入房间: #{inspect(message.data)}")

    data = message.data || %{}
    room_id = Map.get(data, "room_id", "room_default")

    new_state = %{state | room_id: room_id}

    response_data = %{
      "status" => 0,
      "room_id" => room_id,
      "user_id" => state.user_id,
      "seat_id" => :rand.uniform(6),
      "message" => "成功进入房间"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_sc_room_player_enter_p,
      "data" => response_data
    }

    {:reply, response_map, new_state}
  end

  # 处理玩家离开房间 (Game.SC_ROOM_PLAYER_QUIT_P)
  def handle_message(
        %{main_id: @main_proto_game, sub_id: @game_sc_room_player_quit_p} = message,
        state
      ) do
    Logger.info("玩家离开房间: #{inspect(message.data)}")

    new_state = %{state | room_id: nil}

    response_data = %{
      "status" => 0,
      "user_id" => state.user_id,
      "message" => "成功离开房间"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_sc_room_player_quit_p,
      "data" => response_data
    }

    {:reply, response_map, new_state}
  end

  # 处理房间状态设置 (Game.SC_ROOM_SET_STATE_P)
  def handle_message(
        %{main_id: @main_proto_game, sub_id: @game_sc_room_set_state_p} = message,
        state
      ) do
    Logger.info("设置房间状态: #{inspect(message.data)}")

    data = message.data || %{}
    room_state = Map.get(data, "room_state", 0)

    response_data = %{
      "status" => 0,
      "room_id" => state.room_id,
      "room_state" => room_state,
      "message" => "房间状态设置成功"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_sc_room_set_state_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理暂离成功 (Game.SC_ROOM_ZANLI_SUCCESS_P)
  def handle_message(
        %{main_id: @main_proto_game, sub_id: @game_sc_room_zanli_success_p} = message,
        state
      ) do
    Logger.info("玩家暂离成功: #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      "user_id" => state.user_id,
      "room_id" => state.room_id,
      "message" => "暂离成功"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_sc_room_zanli_success_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理匹配结束 (Game.SC_MODE1_PIPEI_OVER_P)
  def handle_message(
        %{main_id: @main_proto_game, sub_id: @game_sc_mode1_pipei_over_p} = message,
        state
      ) do
    Logger.info("匹配结束: #{inspect(message.data)}")

    # 模拟匹配成功，分配房间
    room_id = "room_#{:rand.uniform(1000)}"
    new_state = %{state | room_id: room_id}

    response_data = %{
      "status" => 0,
      "room_id" => room_id,
      "seat_id" => :rand.uniform(6),
      "players" => [
        %{
          "user_id" => state.user_id,
          "nickname" => "玩家1",
          "seat_id" => 1
        },
        %{
          "user_id" => "user_#{:rand.uniform(1000)}",
          "nickname" => "玩家2",
          "seat_id" => 2
        }
      ],
      "message" => "匹配成功，进入游戏"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_sc_mode1_pipei_over_p,
      "data" => response_data
    }

    {:reply, response_map, new_state}
  end

  # 处理退出匹配成功 (Game.SC_MODE1_QUIT_PIPEI_SUCCESS_P)
  def handle_message(
        %{main_id: @main_proto_game, sub_id: @game_sc_mode1_quit_pipei_success_p} = message,
        state
      ) do
    Logger.info("退出匹配成功: #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      "user_id" => state.user_id,
      "message" => "退出匹配成功"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_sc_mode1_quit_pipei_success_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理请求游戏玩家人数表 (Game.CS_GAME_PLAYER_NUM_P)
  def handle_message(
        %{main_id: @main_proto_game, sub_id: @game_cs_game_player_num_p} = message,
        state
      ) do
    Logger.info("收到请求游戏玩家人数表: #{inspect(message.data)}")

    # 模拟各个游戏的玩家人数数据
    game_player_nums = %{
      # Teen Patti
      "1" => %{
        "game_id" => 1,
        "game_name" => "Teen Patti",
        "total_players" => :rand.uniform(500) + 100,
        "online_players" => :rand.uniform(200) + 50,
        "rooms_count" => :rand.uniform(20) + 5
      },
      # Rummy
      "2" => %{
        "game_id" => 2,
        "game_name" => "Rummy",
        "total_players" => :rand.uniform(300) + 80,
        "online_players" => :rand.uniform(150) + 30,
        "rooms_count" => :rand.uniform(15) + 3
      },
      # Andar Bahar
      "3" => %{
        "game_id" => 3,
        "game_name" => "Andar Bahar",
        "total_players" => :rand.uniform(400) + 120,
        "online_players" => :rand.uniform(180) + 40,
        "rooms_count" => :rand.uniform(18) + 4
      },
      # Dragon Tiger
      "4" => %{
        "game_id" => 4,
        "game_name" => "Dragon Tiger",
        "total_players" => :rand.uniform(250) + 60,
        "online_players" => :rand.uniform(120) + 25,
        "rooms_count" => :rand.uniform(12) + 2
      }
    }

    response_data = %{
      "status" => 0,
      "game_player_nums" => game_player_nums,
      "total_games" => map_size(game_player_nums),
      "server_time" => System.system_time(:millisecond),
      "message" => "获取游戏玩家人数成功"
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => @game_sc_game_player_num_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理其他游戏逻辑相关请求（排除已有专门处理器的消息）
  def handle_message(%{main_id: @main_proto_game, sub_id: sub_id} = message, state)
      when sub_id not in [
             @game_cs_mode1_enter_p,
             @game_cs_mode1_enter_pipei_p,
             @game_cs_quit_p,
             @game_cs_huanzhuo_p
           ] do
    Logger.info(
      "🎮 [GAME_GENERIC] 通用游戏消息: [#{message.main_id}][#{sub_id}] - #{inspect(message.data)}"
    )

    # 通用游戏消息处理
    response_data = %{
      # 客户端期望的字段
      "code" => 0,
      # 错误消息（成功时为空）
      "msg" => "",
      "status" => 0,
      "message" => "received",
      "original_sub_id" => sub_id
    }

    response_map = %{
      "mainId" => @main_proto_game,
      "subId" => sub_id + 1,
      "data" => response_data
    }

    Logger.info("🎮 [GAME_GENERIC] 通用响应: MainID=#{@main_proto_game}, SubID=#{sub_id + 1}")

    {:reply, response_map, state}
  end

  # 处理XC协议 - 房间信息
  def handle_message(%{main_id: @main_proto_xc, sub_id: @xc_room_info_p} = message, state) do
    Logger.info("XC房间信息请求: #{inspect(message.data)}")

    response_data = %{
      "room_id" => state.room_id || "xc_room_default",
      "player_count" => :rand.uniform(6),
      "max_players" => 6,
      "game_status" => 0,
      "round" => 1
    }

    response_map = %{
      "mainId" => @main_proto_xc,
      "subId" => @xc_room_info_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理XC协议 - 结算
  def handle_message(%{main_id: @main_proto_xc, sub_id: @xc_jiesuan_p} = message, state) do
    Logger.info("XC结算请求: #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      # 随机输赢金额
      "win_amount" => :rand.uniform(1000) - 500,
      "total_score" => :rand.uniform(100),
      "game_result" => "game_finished"
    }

    response_map = %{
      "mainId" => @main_proto_xc,
      "subId" => @xc_jiesuan_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理XC协议的其他请求
  def handle_message(%{main_id: @main_proto_xc, sub_id: sub_id} = message, state) do
    Logger.info("XC协议消息: [#{message.main_id}][#{sub_id}] - #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      "message" => "xc_received",
      "original_sub_id" => sub_id
    }

    response_map = %{
      "mainId" => @main_proto_xc,
      "subId" => sub_id,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理Money协议 - 设置金币
  def handle_message(
        %{main_id: @main_proto_money, sub_id: @money_sc_set_money_p} = message,
        state
      ) do
    Logger.info("设置金币请求: #{inspect(message.data)}")

    data = message.data || %{}
    money_amount = Map.get(data, "money", 0)

    response_data = %{
      "status" => 0,
      "money" => money_amount,
      "user_id" => state.user_id,
      "message" => "金币设置成功"
    }

    response_map = %{
      "mainId" => @main_proto_money,
      "subId" => @money_sc_set_money_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理Money协议 - 设置钱包金额
  def handle_message(
        %{main_id: @main_proto_money, sub_id: @money_sc_set_walletmoney_p} = message,
        state
      ) do
    Logger.info("设置钱包金额请求: #{inspect(message.data)}")

    data = message.data || %{}
    wallet_money = Map.get(data, "wallet_money", 0)

    response_data = %{
      "status" => 0,
      "wallet_money" => wallet_money,
      "user_id" => state.user_id,
      "message" => "钱包金额设置成功"
    }

    response_map = %{
      "mainId" => @main_proto_money,
      "subId" => @money_sc_set_walletmoney_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理获取金币请求 (Money.CS_GET_MONEY_P)
  def handle_message(
        %{main_id: @main_proto_money, sub_id: @money_cs_get_money_p} = message,
        state
      ) do
    Logger.info("获取金币请求: #{inspect(message.data)}")

    # 模拟获取用户金币信息
    response_data = %{
      "status" => 0,
      "money" => 10000,
      "wallet_money" => 50000,
      "user_id" => state.user_id,
      "message" => "获取金币信息成功"
    }

    response_map = %{
      "mainId" => @main_proto_money,
      "subId" => @money_sc_get_money_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理变更金币请求 (Money.CS_CHANGE_MONEY_P)
  def handle_message(
        %{main_id: @main_proto_money, sub_id: @money_cs_change_money_p} = message,
        state
      ) do
    Logger.info("变更金币请求: #{inspect(message.data)}")

    data = message.data || %{}
    change_amount = Map.get(data, "amount", 0)
    # 1-增加, 2-减少
    change_type = Map.get(data, "type", 1)

    response_data = %{
      "status" => 0,
      "change_amount" => change_amount,
      "change_type" => change_type,
      "new_money" => 10000 + if(change_type == 1, do: change_amount, else: -change_amount),
      "user_id" => state.user_id,
      "message" => "金币变更成功"
    }

    response_map = %{
      "mainId" => @main_proto_money,
      "subId" => @money_sc_change_money_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理Money协议的其他请求
  def handle_message(%{main_id: @main_proto_money, sub_id: sub_id} = message, state) do
    Logger.info("Money协议消息: [#{message.main_id}][#{sub_id}] - #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      "message" => "money_received",
      "original_sub_id" => sub_id
    }

    response_map = %{
      "mainId" => @main_proto_money,
      "subId" => sub_id,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== Pay 协议处理 ====================

  # 处理获取支付列表请求 (Pay.CS_GET_PAY_LIST_P)
  def handle_message(%{main_id: @main_proto_pay, sub_id: @pay_cs_get_pay_list_p} = message, state) do
    Logger.info("获取支付列表请求: #{inspect(message.data)}")

    # 模拟支付商品列表
    pay_list = [
      %{
        "id" => 1,
        "name" => "金币包小",
        "price" => 6.0,
        "money" => 1000,
        "bonus" => 100,
        "icon" => "coin_small.png"
      },
      %{
        "id" => 2,
        "name" => "金币包中",
        "price" => 30.0,
        "money" => 6000,
        "bonus" => 1000,
        "icon" => "coin_medium.png"
      },
      %{
        "id" => 3,
        "name" => "金币包大",
        "price" => 98.0,
        "money" => 20000,
        "bonus" => 5000,
        "icon" => "coin_large.png"
      }
    ]

    response_data = %{
      "status" => 0,
      "pay_list" => pay_list,
      "message" => "获取支付列表成功"
    }

    response_map = %{
      "mainId" => @main_proto_pay,
      "subId" => @pay_sc_get_pay_list_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理支付请求 (Pay.CS_PAY_P)
  def handle_message(%{main_id: @main_proto_pay, sub_id: @pay_cs_pay_p} = message, state) do
    Logger.info("支付请求: #{inspect(message.data)}")

    data = message.data || %{}
    pay_id = Map.get(data, "pay_id", 0)
    pay_method = Map.get(data, "pay_method", "alipay")

    # 模拟支付处理
    response_data = %{
      "status" => 0,
      "pay_id" => pay_id,
      "order_id" => "order_#{:rand.uniform(1_000_000)}",
      "pay_url" => "https://example.com/pay/#{pay_id}",
      "message" => "支付订单创建成功"
    }

    response_map = %{
      "mainId" => @main_proto_pay,
      "subId" => @pay_sc_pay_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理检查支付状态请求 (Pay.CS_CHECK_PAY_P)
  def handle_message(%{main_id: @main_proto_pay, sub_id: @pay_cs_check_pay_p} = message, state) do
    Logger.info("检查支付状态请求: #{inspect(message.data)}")

    data = message.data || %{}
    order_id = Map.get(data, "order_id", "")

    # 模拟支付状态检查
    # 70%概率支付成功
    pay_status = :rand.uniform(10) > 3

    response_data = %{
      "status" => 0,
      "order_id" => order_id,
      # 1-成功, 0-处理中
      "pay_status" => if(pay_status, do: 1, else: 0),
      "message" => if(pay_status, do: "支付成功", else: "支付处理中")
    }

    response_map = %{
      "mainId" => @main_proto_pay,
      "subId" => @pay_sc_check_pay_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理Pay协议的其他请求
  def handle_message(%{main_id: @main_proto_pay, sub_id: sub_id} = message, state) do
    Logger.info("Pay协议消息: [#{message.main_id}][#{sub_id}] - #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      "message" => "pay_received",
      "original_sub_id" => sub_id
    }

    response_map = %{
      "mainId" => @main_proto_pay,
      "subId" => sub_id + 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== Email 协议处理 ====================

  # 处理获取邮件列表请求 (Email.CS_GET_EMAIL_LIST_P)
  def handle_message(
        %{main_id: @main_proto_email, sub_id: @email_cs_get_email_list_p} = message,
        state
      ) do
    Logger.info("获取邮件列表请求: #{inspect(message.data)}")

    # 模拟邮件列表
    email_list = [
      %{
        "id" => 1,
        "title" => "欢迎来到游戏",
        "sender" => "系统",
        "content" => "欢迎您加入我们的游戏世界！",
        "is_read" => false,
        "has_attachment" => true,
        "send_time" => System.system_time(:millisecond) - 86_400_000,
        "attachments" => [
          %{"type" => "money", "amount" => 1000},
          %{"type" => "item", "item_id" => 1, "count" => 5}
        ]
      },
      %{
        "id" => 2,
        "title" => "每日签到奖励",
        "sender" => "系统",
        "content" => "恭喜您获得每日签到奖励！",
        "is_read" => true,
        "has_attachment" => false,
        "send_time" => System.system_time(:millisecond) - 3_600_000,
        "attachments" => []
      }
    ]

    response_data = %{
      "status" => 0,
      "email_list" => email_list,
      "total_count" => length(email_list),
      "unread_count" => Enum.count(email_list, fn email -> !email["is_read"] end),
      "message" => "获取邮件列表成功"
    }

    response_map = %{
      "mainId" => @main_proto_email,
      "subId" => @email_sc_get_email_list_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理读取邮件请求 (Email.CS_READ_EMAIL_P)
  def handle_message(
        %{main_id: @main_proto_email, sub_id: @email_cs_read_email_p} = message,
        state
      ) do
    Logger.info("读取邮件请求: #{inspect(message.data)}")

    data = message.data || %{}
    email_id = Map.get(data, "email_id", 0)

    response_data = %{
      "status" => 0,
      "email_id" => email_id,
      "is_read" => true,
      "message" => "邮件已标记为已读"
    }

    response_map = %{
      "mainId" => @main_proto_email,
      "subId" => @email_sc_read_email_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理删除邮件请求 (Email.CS_DELETE_EMAIL_P)
  def handle_message(
        %{main_id: @main_proto_email, sub_id: @email_cs_delete_email_p} = message,
        state
      ) do
    Logger.info("删除邮件请求: #{inspect(message.data)}")

    data = message.data || %{}
    email_id = Map.get(data, "email_id", 0)

    response_data = %{
      "status" => 0,
      "email_id" => email_id,
      "message" => "邮件删除成功"
    }

    response_map = %{
      "mainId" => @main_proto_email,
      "subId" => @email_sc_delete_email_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理Email协议的其他请求
  def handle_message(%{main_id: @main_proto_email, sub_id: sub_id} = message, state) do
    Logger.info("Email协议消息: [#{message.main_id}][#{sub_id}] - #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      "message" => "email_received",
      "original_sub_id" => sub_id
    }

    response_map = %{
      "mainId" => @main_proto_email,
      "subId" => sub_id + 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== DbServer 协议处理 ====================

  # 处理客服消息请求 (DbServer.CS_CUSTSRV_REPLY_P)
  def handle_message(
        %{main_id: @main_proto_db_server, sub_id: @db_server_cs_custsrv_reply_p} = message,
        state
      ) do
    Logger.info("客服消息请求: #{inspect(message.data)}")

    # 模拟客服消息列表
    customer_service_messages = [
      %{
        "id" => 1,
        "title" => "欢迎咨询",
        "content" => "您好，有什么可以帮助您的吗？",
        "sender" => "客服小助手",
        "send_time" => System.system_time(:millisecond) - 3_600_000,
        "is_read" => false
      },
      %{
        "id" => 2,
        "title" => "游戏指南",
        "content" => "这里是游戏的基本操作指南...",
        "sender" => "客服小助手",
        "send_time" => System.system_time(:millisecond) - 7_200_000,
        "is_read" => true
      }
    ]

    response_data = %{
      "status" => 0,
      "messages" => customer_service_messages,
      "total_count" => length(customer_service_messages),
      "unread_count" => Enum.count(customer_service_messages, fn msg -> !msg["is_read"] end)
    }

    response_map = %{
      "mainId" => @main_proto_db_server,
      "subId" => @db_server_sc_custsrv_reply_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理充值消息列表请求 (DbServer.CS_NEW_CHARGE_LIST_P)
  def handle_message(
        %{main_id: @main_proto_db_server, sub_id: @db_server_cs_new_charge_list_p} = message,
        state
      ) do
    Logger.info("充值消息列表请求: #{inspect(message.data)}")

    # 模拟充值记录
    charge_list = [
      %{
        "id" => 1,
        "amount" => 30.0,
        "money" => 6000,
        "bonus" => 1000,
        # 1-成功, 0-处理中, -1-失败
        "status" => 1,
        "charge_time" => System.system_time(:millisecond) - 1_800_000,
        "order_id" => "charge_#{:rand.uniform(1_000_000)}"
      },
      %{
        "id" => 2,
        "amount" => 6.0,
        "money" => 1000,
        "bonus" => 100,
        "status" => 1,
        "charge_time" => System.system_time(:millisecond) - 86_400_000,
        "order_id" => "charge_#{:rand.uniform(1_000_000)}"
      }
    ]

    response_data = %{
      "status" => 0,
      "charge_list" => charge_list,
      "total_count" => length(charge_list),
      "total_amount" => Enum.reduce(charge_list, 0, fn charge, acc -> acc + charge["amount"] end)
    }

    response_map = %{
      "mainId" => @main_proto_db_server,
      "subId" => @db_server_sc_new_charge_list_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理礼包查询请求 (DbServer.CD_QUERY_GIFT_PACK_P)
  def handle_message(
        %{main_id: @main_proto_db_server, sub_id: @db_server_cd_query_gift_pack_p} = message,
        state
      ) do
    Logger.info("礼包查询请求: #{inspect(message.data)}")

    # 模拟礼包信息
    gift_packs = [
      %{
        "id" => 1,
        "name" => "新手礼包",
        "description" => "为新手玩家准备的丰厚礼包",
        # 0表示免费
        "price" => 0,
        "items" => [
          %{"type" => "money", "amount" => 5000},
          %{"type" => "item", "item_id" => 1, "count" => 10}
        ],
        "is_available" => true,
        "expire_time" => System.system_time(:millisecond) + 86_400_000 * 7
      },
      %{
        "id" => 2,
        "name" => "豪华礼包",
        "description" => "包含大量金币和道具的豪华礼包",
        "price" => 98.0,
        "items" => [
          %{"type" => "money", "amount" => 50000},
          %{"type" => "item", "item_id" => 2, "count" => 50}
        ],
        "is_available" => true,
        "expire_time" => System.system_time(:millisecond) + 86_400_000 * 30
      }
    ]

    response_data = %{
      "status" => 0,
      "gift_packs" => gift_packs,
      "total_count" => length(gift_packs)
    }

    response_map = %{
      "mainId" => @main_proto_db_server,
      "subId" => @db_server_dc_query_gift_pack_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理获取代理配置 (QMAgent.CD_GET_AGENT_CONFIG_P 通过DBServer)
  def handle_message(
        %{main_id: @main_proto_db_server, sub_id: @qm_agent_cd_get_agent_config_p} = message,
        state
      ) do
    Logger.info("收到获取代理配置请求: #{inspect(message.data)}")

    # 模拟代理配置数据
    response_data = %{
      "status" => 0,
      "agent_config" => %{
        # 佣金比例 5%
        "commission_rate" => 0.05,
        # 最小提现金额
        "min_withdraw" => 100,
        # 最大提现金额
        "max_withdraw" => 10000,
        # 提现手续费 2%
        "withdraw_fee" => 0.02,
        # 一级代理佣金比例
        "level_1_rate" => 0.03,
        # 二级代理佣金比例
        "level_2_rate" => 0.02,
        # 三级代理佣金比例
        "level_3_rate" => 0.01,
        # 推广奖励
        "promotion_bonus" => 50,
        # 是否启用代理功能
        "is_agent_enabled" => true
      },
      "user_agent_info" => %{
        # 当前用户是否为代理
        "is_agent" => false,
        # 代理等级
        "agent_level" => 0,
        # 总佣金
        "total_commission" => 0,
        # 可提现佣金
        "available_commission" => 0,
        # 直推人数
        "direct_members" => 0,
        # 总团队人数
        "total_members" => 0
      },
      "message" => "获取代理配置成功"
    }

    response_map = %{
      "mainId" => @main_proto_db_server,
      "subId" => @qm_agent_dc_get_agent_config_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # 处理DbServer协议的其他请求
  def handle_message(%{main_id: @main_proto_db_server, sub_id: sub_id} = message, state) do
    Logger.info("DbServer协议消息: [#{message.main_id}][#{sub_id}] - #{inspect(message.data)}")

    response_data = %{
      "status" => 0,
      "message" => "db_server_received",
      "original_sub_id" => sub_id
    }

    response_map = %{
      "mainId" => @main_proto_db_server,
      "subId" => sub_id + 1,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== MailManager 协议处理 ====================

  # 处理请求新邮件数量 (MailManager.CS_REQUEST_NEW_MAIL_COUNT_P)
  def handle_message(
        %{main_id: @main_proto_mail_manager, sub_id: @mail_manager_cs_request_new_mail_count_p} =
          message,
        state
      ) do
    Logger.info("收到请求新邮件数量: #{inspect(message.data)}")

    # 模拟新邮件数量
    response_data = %{
      "status" => 0,
      # 随机0-5封新邮件
      "count" => :rand.uniform(5),
      "message" => "获取新邮件数量成功"
    }

    response_map = %{
      "mainId" => @main_proto_mail_manager,
      "subId" => @mail_manager_sc_request_new_mail_count_p,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== Task 协议处理 ====================

  # 处理获取今日比赛列表 (Task.CS_GET_TODAY_MATCH_LIST)
  def handle_message(
        %{main_id: @main_proto_task, sub_id: @task_cs_get_today_match_list} = message,
        state
      ) do
    Logger.info("收到获取今日比赛列表请求: #{inspect(message.data)}")

    # 模拟今日比赛数据
    current_time = System.system_time(:millisecond)

    today_matches = [
      %{
        "id" => 1,
        "name" => "Teen Patti Championship",
        "game_id" => 1,
        # 30分钟后开始
        "start_time" => current_time + 1_800_000,
        # 2小时后结束
        "end_time" => current_time + 7_200_000,
        # 1: 未开始, 2: 进行中, 3: 已结束
        "status" => 1,
        "entry_fee" => 100,
        "prize_pool" => 10000,
        "max_players" => 100,
        "current_players" => 45
      },
      %{
        "id" => 2,
        "name" => "Rummy Tournament",
        "game_id" => 2,
        # 1小时后开始
        "start_time" => current_time + 3_600_000,
        # 3小时后结束
        "end_time" => current_time + 10_800_000,
        "status" => 1,
        "entry_fee" => 50,
        "prize_pool" => 5000,
        "max_players" => 50,
        "current_players" => 23
      }
    ]

    response_data = %{
      "status" => 0,
      "matches" => today_matches,
      "total_count" => length(today_matches),
      "server_time" => current_time,
      "message" => "获取今日比赛列表成功"
    }

    response_map = %{
      "mainId" => @main_proto_task,
      "subId" => @task_sc_get_today_match_list,
      "data" => response_data
    }

    {:reply, response_map, state}
  end

  # ==================== 通用消息处理 ====================

  def handle_message(%{type: "ping"} = _message, state) do
    # 处理PING消息
    response_map = %{
      "mainId" => 0,
      "subId" => 19,
      "data" => %{
        "time" => System.system_time(:millisecond)
      }
    }

    {:reply, response_map, state}
  end

  # 处理未实现的主协议
  def handle_message(%{main_id: main_id, sub_id: sub_id} = message, state)
      when is_integer(main_id) and is_integer(sub_id) do
    Logger.warning(
      "🔴 [UNIMPLEMENTED] 收到未实现的协议 - MainID: #{main_id}, SubID: #{sub_id}, Data: #{inspect(message.data)}"
    )

    # 根据主协议ID返回相应的通用响应
    protocol_name =
      case main_id do
        @main_proto_find_psw -> "find_password"
        @main_proto_id_record -> "id_record"
        @main_proto_level_exp -> "level_exp"
        @main_proto_phone -> "phone"
        @main_proto_lottery -> "lottery"
        @main_proto_shop -> "shop"
        @main_proto_bag -> "bag"
        @main_proto_friend -> "friend"
        @main_proto_notice -> "notice"
        @main_proto_activity -> "activity"
        @main_proto_sign -> "sign"
        @main_proto_share -> "share"
        @main_proto_feedback -> "feedback"
        @main_proto_task_manager -> "task_manager"
        @main_proto_present -> "present"
        @main_proto_present_manager -> "present_manager"
        @main_proto_game_record -> "game_record"
        @main_proto_present_notice -> "present_notice"
        @main_proto_vip -> "vip"
        @main_proto_player_timer -> "player_timer"
        @main_proto_card -> "card"
        @main_proto_account_info -> "account_info"
        @main_proto_chat_server -> "chat_server"
        @main_proto_local_server -> "local_server"
        @main_proto_rank -> "rank"
        @main_proto_task -> "task"
        @main_proto_hall_activity -> "hall_activity"
        _ -> "unknown_protocol"
      end

    response_data = %{
      "status" => 0,
      "message" => "#{protocol_name}_received",
      "original_main_id" => main_id,
      "original_sub_id" => sub_id,
      "note" => "该协议处理函数尚未实现"
    }

    response_map = %{
      "mainId" => main_id,
      "subId" => sub_id + 1,
      "data" => response_data
    }

    Logger.info(
      "🟡 [UNIMPLEMENTED_RESPONSE] 返回未实现协议的默认响应 - MainID: #{main_id}, SubID: #{sub_id + 1}"
    )

    {:reply, response_map, state}
  end

  def handle_message(message, state) do
    # 处理其他未知类型的消息
    Logger.error("🔴 [UNKNOWN_FORMAT] 未知消息格式: #{inspect(message)}")

    response_map = %{
      "mainId" => 0,
      "subId" => 0,
      "data" => %{
        "type" => "error",
        "code" => "unknown_message",
        "message" => "未知消息类型或格式错误"
      }
    }

    Logger.info("🔴 [ERROR_RESPONSE] 返回错误响应 - MainID: 0, SubID: 0")
    {:reply, response_map, state}
  end

  @doc """
  处理发送到客户端的消息

  ## 参数
    - main_id: 主协议ID
    - sub_id: 子协议ID
    - data: 要发送的数据
    - state: 当前WebSocket连接状态

  ## 返回
    - {:reply, response_map, state} WebSocket回复（map格式）
  """
  def send_message(main_id, sub_id, data, state) do
    response_map = %{
      "mainId" => main_id,
      "subId" => sub_id,
      "data" => data
    }

    {:reply, response_map, state}
  end










end
