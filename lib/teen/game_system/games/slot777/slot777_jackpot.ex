defmodule <PERSON>p<PERSON><PERSON>.Teen.GameSystem.Games.Slot777.Slot777Jackpot do
  @moduledoc """
  Slot777 Jackpot管理模块
  负责：
  - Jackpot池管理
  - 中奖记录
  - 全服广播
  """

  use GenServer
  require Logger

  # Jackpot状态结构
  defstruct [
    :current_amount,      # 当前奖池金额
    :base_amount,         # 基础奖池金额
    :records,             # 中奖记录
    :last_winner,         # 最后中奖者
    :total_contributed    # 总贡献金额
  ]

  # 默认配置
  @default_base_amount 100000
  @contribution_rate 0.01  # 1%的下注进入奖池
  @max_records 100        # 最多保存100条记录

  ## Client API

  @doc """
  启动Jackpot管理器
  """
  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @doc """
  获取当前Jackpot金额
  """
  def get_current_amount() do
    GenServer.call(__MODULE__, :get_current_amount)
  end

  @doc """
  贡献到Jackpot池
  """
  def contribute(bet_amount) do
    contribution = trunc(bet_amount * @contribution_rate)
    GenServer.cast(__MODULE__, {:contribute, contribution})
    contribution
  end

  @doc """
  触发Jackpot中奖
  """
  def trigger_jackpot(player_id, player_name, bet_amount, seven_count) do
    GenServer.call(__MODULE__, {:trigger_jackpot, player_id, player_name, bet_amount, seven_count})
  end

  @doc """
  获取Jackpot中奖记录
  """
  def get_records() do
    GenServer.call(__MODULE__, :get_records)
  end

  @doc """
  重置Jackpot池到基础金额
  """
  def reset_to_base() do
    GenServer.cast(__MODULE__, :reset_to_base)
  end

  ## Server Callbacks

  @impl true
  def init(_opts) do
    # 初始化状态
    state = %__MODULE__{
      current_amount: @default_base_amount,
      base_amount: @default_base_amount,
      records: [],
      last_winner: nil,
      total_contributed: 0
    }

    Logger.info("🎰 [JACKPOT] Jackpot管理器启动，初始金额: #{state.current_amount}")

    # 定期广播Jackpot金额
    schedule_broadcast()

    {:ok, state}
  end

  @impl true
  def handle_call(:get_current_amount, _from, state) do
    {:reply, state.current_amount, state}
  end

  @impl true
  def handle_call({:trigger_jackpot, player_id, player_name, bet_amount, seven_count}, _from, state) do
    # 计算Jackpot奖金
    jackpot_amount = calculate_jackpot_amount(state.current_amount, seven_count)

    # 创建中奖记录
    record = %{
      player_id: player_id,
      player_name: player_name,
      amount: jackpot_amount,
      seven_count: seven_count,
      bet_amount: bet_amount,
      timestamp: DateTime.utc_now(),
      type: get_jackpot_type(seven_count)
    }

    # 更新状态
    new_records = [record | Enum.take(state.records, @max_records - 1)]
    new_amount = max(state.base_amount, state.current_amount - jackpot_amount)

    new_state = %{state |
      current_amount: new_amount,
      records: new_records,
      last_winner: record
    }

    Logger.info("🎰 [JACKPOT] 玩家 #{player_name}(#{player_id}) 中奖！金额: #{jackpot_amount}, 7的数量: #{seven_count}")

    # 广播Jackpot中奖消息
    broadcast_jackpot_win(record)

    # 广播更新后的Jackpot金额
    broadcast_jackpot_amount(new_state.current_amount)

    {:reply, {:ok, jackpot_amount}, new_state}
  end

  @impl true
  def handle_call(:get_records, _from, state) do
    {:reply, {:ok, state.records}, state}
  end

  @impl true
  def handle_cast({:contribute, amount}, state) do
    new_amount = state.current_amount + amount
    new_total = state.total_contributed + amount

    new_state = %{state |
      current_amount: new_amount,
      total_contributed: new_total
    }

    Logger.debug("🎰 [JACKPOT] 奖池增加: #{amount}, 当前总额: #{new_amount}")

    {:noreply, new_state}
  end

  @impl true
  def handle_cast(:reset_to_base, state) do
    new_state = %{state |
      current_amount: state.base_amount,
      total_contributed: 0
    }

    Logger.info("🎰 [JACKPOT] 奖池重置到基础金额: #{state.base_amount}")

    {:noreply, new_state}
  end

  @impl true
  def handle_info(:broadcast_jackpot, state) do
    # 广播当前Jackpot金额
    broadcast_jackpot_amount(state.current_amount)

    # 安排下次广播
    schedule_broadcast()

    {:noreply, state}
  end

  ## Private Functions

  @doc """
  计算Jackpot奖金金额
  """
  defp calculate_jackpot_amount(current_amount, seven_count) do
    case seven_count do
      count when count >= 5 ->
        # 5个7 = 获得80%的奖池
        trunc(current_amount * 0.8)

      4 ->
        # 4个7 = 获得50%的奖池
        trunc(current_amount * 0.5)

      3 ->
        # 3个7 = 获得20%的奖池
        trunc(current_amount * 0.2)

      _ ->
        0
    end
  end

  @doc """
  获取Jackpot类型
  """
  defp get_jackpot_type(seven_count) do
    case seven_count do
      count when count >= 5 -> "MEGA_JACKPOT"
      4 -> "MAJOR_JACKPOT"
      3 -> "MINOR_JACKPOT"
      _ -> "NO_JACKPOT"
    end
  end

  @doc """
  广播Jackpot中奖消息
  """
  defp broadcast_jackpot_win(record) do
    message = %{
      "mainId" => 40,
      "subId" => 1006,  # SC_SLOT777_JPAWARD_P
      "data" => %{
        "playerid" => record.player_id,
        "name" => record.player_name,
        "headid" => 1,  # 默认头像
        "wxheadurl" => "",
        "winscore" => record.amount,
        "jackpot_type" => record.type,
        "seven_count" => record.seven_count
      }
    }

    # 广播给所有在线玩家
    CypridinaWeb.Endpoint.broadcast!("game:slot777", "jackpot_win", message)

    Logger.info("🎰 [JACKPOT] 广播中奖消息: #{record.player_name} 中奖 #{record.amount}")
  end

  @doc """
  广播Jackpot金额更新
  """
  defp broadcast_jackpot_amount(amount) do
    message = %{
      "mainId" => 40,
      "subId" => 1005,  # SC_SLOT777_JACKPOT_P
      "data" => %{
        "jackpot" => amount
      }
    }

    # 广播给所有在线玩家
    CypridinaWeb.Endpoint.broadcast!("game:slot777", "jackpot_update", message)

    Logger.debug("🎰 [JACKPOT] 广播奖池金额: #{amount}")
  end

  @doc """
  安排下次广播
  """
  defp schedule_broadcast() do
    # 每30秒广播一次Jackpot金额
    Process.send_after(self(), :broadcast_jackpot, 30_000)
  end

  @doc """
  格式化中奖记录用于前端显示
  """
  def format_records_for_frontend(records) do
    Enum.map(records, fn record ->
      %{
        "playerid" => record.player_id,
        "name" => record.player_name,
        "amount" => record.amount,
        "type" => record.type,
        "timestamp" => DateTime.to_unix(record.timestamp),
        "seven_count" => record.seven_count
      }
    end)
  end
end
