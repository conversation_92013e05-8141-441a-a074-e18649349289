defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.Slot777.Slot777Protocol do
  @moduledoc """
  Slot777游戏协议处理
  """

  require Logger
  alias <PERSON><PERSON>ridina.Teen.GameSystem.RoomManager
  alias Cypridina.Teen.GameSystem.Games.Slot777.Slot777Room

  # 协议ID定义
  @main_id 40  # Slot777主协议ID

  @doc """
  处理加入Slot777房间请求
  """
  def handle_join_room(%{main_id: 40, sub_id: 1} = message, state) do
    Logger.info("🎰 [SLOT777_PROTOCOL] 处理加入房间请求: #{inspect(message.data)}")
    Logger.info("🎰 [SLOT777_PROTOCOL] 状态信息 - player_id: #{inspect(state.player_id)}, user_id: #{inspect(state.user_id)}")

    room_id = Map.get(message.data, "room_id")
    user_info = Map.get(message.data, "user_info", %{})
    # 使用player_id（numeric_id）而不是user_id（UUID）
    actual_user_id = state.player_id || state.user_id

    Logger.info("🎰 [SLOT777_PROTOCOL] 最终使用的user_id: #{inspect(actual_user_id)}")

    cond do
      # 已经在房间中
      state.current_room != nil ->
        response = build_response(40, 11, %{
          "code" => 1,
          "msg" => "已经在其他房间中"
        })

        {:reply, response, state}

      # 指定了房间ID，尝试加入
      room_id != nil && room_id != "" && RoomManager.room_exists?(room_id) ->
        # 加入现有房间
        case Slot777Room.join_room(room_id, actual_user_id, user_info) do
          {:ok, :joined} ->
            new_state = Map.merge(state, %{current_room: room_id, game_type: :slot777})

            response = build_response(40, 11, %{
              "code" => 0,
              "msg" => "加入房间成功",
              "room_id" => room_id,
              "is_creator" => false
            })

            {:reply, response, new_state}

          {:ok, :rejoined} ->
            new_state = Map.merge(state, %{current_room: room_id, game_type: :slot777})

            response = build_response(40, 11, %{
              "code" => 0,
              "msg" => "重新加入房间成功",
              "room_id" => room_id,
              "is_creator" => false
            })

            {:reply, response, new_state}

          {:error, reason} ->
            response = build_response(40, 11, %{
              "code" => 1,
              "msg" => "加入房间失败: #{reason}"
            })

            {:reply, response, state}
        end

      # 没有指定房间ID，创建新房间
      true ->
        case create_slot777_room(actual_user_id, user_info) do
          {:ok, room_id} ->
            # 创建成功后加入房间
            case Slot777Room.join_room(room_id, actual_user_id, user_info) do
              {:ok, :joined} ->
                new_state = Map.merge(state, %{current_room: room_id, game_type: :slot777})

                response = build_response(40, 11, %{
                  "code" => 0,
                  "msg" => "创建并加入房间成功",
                  "room_id" => room_id,
                  "is_creator" => true
                })

                {:reply, response, new_state}

              {:error, reason} ->
                response = build_response(40, 11, %{
                  "code" => 1,
                  "msg" => "加入房间失败: #{reason}"
                })

                {:reply, response, state}
            end

          {:error, reason} ->
            response = build_response(40, 11, %{
              "code" => 1,
              "msg" => "创建房间失败: #{reason}"
            })

            {:reply, response, state}
        end
    end
  end

  @doc """
  处理离开Slot777房间请求
  """
  def handle_leave_room(%{main_id: 40, sub_id: 2} = message, state) do
    Logger.info("🎰 [SLOT777_PROTOCOL] 处理离开房间请求: #{inspect(message.data)}")

    if state.current_room do
      actual_user_id = state.player_id || state.user_id
      case Slot777Room.leave_room(state.current_room, actual_user_id) do
        {:ok, :left} ->
          new_state = Map.merge(state, %{current_room: nil, game_type: nil})

          response = build_response(40, 12, %{
            "code" => 0,
            "msg" => "离开房间成功"
          })

          {:reply, response, new_state}

        {:error, reason} ->
          response = build_response(40, 12, %{
            "code" => 1,
            "msg" => "离开房间失败: #{reason}"
          })

          {:reply, response, state}
      end
    else
      response = build_response(40, 12, %{
        "code" => 1,
        "msg" => "未在任何房间中"
      })

      {:reply, response, state}
    end
  end

  @doc """
  处理游戏开始请求 (CS_SLOT777_GAMESTART_P)
  """
  def handle_game_start(%{main_id: 40, sub_id: 1000} = message, state) do
    Logger.info("🎰 [SLOT777_PROTOCOL] 处理游戏开始请求: #{inspect(message.data)}")

    if state.current_room do
      odds = Map.get(message.data, "odds", 1)
      actual_user_id = state.player_id || state.user_id

      case Slot777Room.start_game(state.current_room, actual_user_id, odds) do
        {:ok, game_result} ->
          response = build_response(40, 1001, game_result)
          {:reply, response, state}

        {:error, reason} ->
          response = build_response(40, 1001, %{
            "code" => 1,
            "msg" => "游戏开始失败: #{reason}"
          })
          {:reply, response, state}
      end
    else
      response = build_response(40, 1001, %{
        "code" => 1,
        "msg" => "未在任何房间中"
      })
      {:reply, response, state}
    end
  end

  @doc """
  处理切换下注倍率请求 (CS_SLOTS_SWITCH_BET_P)
  """
  def handle_switch_bet(%{main_id: 40, sub_id: 1009} = message, state) do
    Logger.info("🎰 [SLOT777_PROTOCOL] 处理切换下注倍率请求: #{inspect(message.data)}")

    if state.current_room do
      odds = Map.get(message.data, "odds", 1)
      actual_user_id = state.player_id || state.user_id

      case Slot777Room.switch_bet(state.current_room, actual_user_id, odds) do
        {:ok, result} ->
          response = build_response(40, 1009, result)
          {:reply, response, state}

        {:error, reason} ->
          response = build_response(40, 1009, %{
            "code" => 1,
            "msg" => "切换下注失败: #{reason}"
          })
          {:reply, response, state}
      end
    else
      response = build_response(40, 1009, %{
        "code" => 1,
        "msg" => "未在任何房间中"
      })
      {:reply, response, state}
    end
  end

  @doc """
  处理Jackpot记录请求 (CS_SLOT777_JPLIST_P)
  """
  def handle_jackpot_list(%{main_id: 40, sub_id: 1003} = message, state) do
    Logger.info("🎰 [SLOT777_PROTOCOL] 处理Jackpot记录请求: #{inspect(message.data)}")

    case Slot777Room.get_jackpot_records() do
      {:ok, records} ->
        response = build_response(40, 1004, %{
          "records" => records
        })
        {:reply, response, state}

      {:error, reason} ->
        response = build_response(40, 1004, %{
          "code" => 1,
          "msg" => "获取Jackpot记录失败: #{reason}"
        })
        {:reply, response, state}
    end
  end

  # 创建Slot777房间
  defp create_slot777_room(creator_id, user_info) do
    room_config = %{
      max_players: 1,  # 单人游戏
      min_players: 1,
      min_bet: 10,
      max_bet: 10000,
      odds_config: [1, 2, 5, 10, 20, 50, 100]
    }

    case RoomManager.create_room(:slot777, creator_id, room_config) do
      {:ok, room_id} -> {:ok, room_id}
      {:error, reason} -> {:error, reason}
    end
  end

  # 构建响应
  defp build_response(main_id, sub_id, data) do
    %{
      "mainId" => main_id,
      "subId" => sub_id,
      "data" => data
    }
  end
end
