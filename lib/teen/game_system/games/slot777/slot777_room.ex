defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.Slot777.Slot777Room do
  @moduledoc """
  Slot777游戏房间实现
  """

  use <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.RoomBase, game_type: :slot777
  require Logger

  alias <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.RoomManager
  alias CypridinaWeb.GameChannel
  alias Cypridina.Teen.GameSystem.Games.Slot777.Slot777GameLogic
  alias <PERSON>pridina.Teen.GameSystem.Games.Slot777.Slot777Jackpot
  alias Cypridina.Accounts.User
  alias Cypridina.Accounts

  # 用户信息获取辅助函数
  @doc """
  根据数字ID获取用户的UUID和相关信息
  直接使用数据库中的numeric_id，不进行转换
  返回 {uuid, numeric_id} 或 {:error, reason}
  """
  defp get_user_info(numeric_id) when is_integer(numeric_id) do
    Logger.info("🎰 [SLOT777_ROOM] 查询用户信息 - numeric_id: #{inspect(numeric_id)}")
    # 直接使用数字ID查询用户信息
    case User.get_by_numeric_id(numeric_id) do
      {:ok, user} ->
        Logger.info("🎰 [SLOT777_ROOM] 找到用户 - UUID: #{user.id}, numeric_id: #{user.numeric_id}")
        {user.id, user.numeric_id}
      {:error, reason} ->
        Logger.error("🎰 [SLOT777_ROOM] 用户查询失败 - numeric_id: #{numeric_id}, 原因: #{inspect(reason)}")
        {:error, "用户不存在"}
    end
  end

  defp get_user_info(invalid_id) do
    type_info = if is_struct(invalid_id) do
      invalid_id.__struct__
    else
      :primitive
    end
    Logger.error("🎰 [SLOT777_ROOM] 无效的用户ID类型 - 值: #{inspect(invalid_id)}, 类型: #{inspect(type_info)}")
    {:error, "无效的用户ID"}
  end

  @doc """
  根据数字ID获取用户的真实积分
  """
  defp get_user_points(numeric_id) when is_integer(numeric_id) do
    case User.get_by_numeric_id(numeric_id) do
      {:ok, user} ->
        case Accounts.get_user_points(user.id) do
          points when is_integer(points) -> points
          _ -> 1_000_000  # 默认积分
        end
      {:error, _} -> 1_000_000  # 默认积分
    end
  end

  defp get_user_points(_), do: 1_000_000

  # 初始化游戏逻辑
  @impl true
  def init_game_logic(state) do
    Logger.info("🎰 [SLOT777] 初始化游戏房间: #{state.id}")

    game_config = %{
      # 最小下注
      min_bet: Map.get(state.config, :min_bet, 10),
      # 最大下注
      max_bet: Map.get(state.config, :max_bet, 10000),
      # 赔率配置
      odds_config: Map.get(state.config, :odds_config, [1, 2, 5, 10, 20, 50, 100]),
      # 底分
      difen: 10
    }

    game_data = %{
      # 游戏配置
      config: game_config,
      # 当前状态
      status: :waiting,
      # 玩家下注记录
      bets: %{},
      # 当前玩家下注倍率
      current_odds: %{},
      # 游戏结果
      results: [],
      # 当前回合
      current_round: 0,
      # 免费游戏状态
      free_games: %{},
      # Jackpot池
      jackpot_amount: Slot777Jackpot.get_current_amount()
    }

    %{state | game_data: game_data}
  end

  # 玩家加入房间后的处理
  @impl true
  def on_player_joined(state, user_id, user_info) do
    Logger.info("🎰 [SLOT777] 玩家加入: #{user_id}")

    # 直接使用传入的user_id作为numeric_id，不进行转换
    {uuid, numeric_id, player_money} = case get_user_info(user_id) do
      {user_uuid, user_numeric_id} ->
        # 真实玩家，从数据库获取积分
        real_money = get_user_points(user_numeric_id)
        {user_uuid, user_numeric_id, real_money}

      {:error, _} ->
        # 机器人或游客，使用默认值
        if is_integer(user_id) and user_id < 0 do
          # 机器人使用随机积分
          robot_money = 10000 + :rand.uniform(50000)
          {nil, user_id, robot_money}
        else
          # 游客使用固定积分
          {nil, user_id, 1_000_000}
        end
    end

    # 更新玩家信息，存储UUID和数字ID
    updated_user_info = user_info
    |> Map.put(:money, player_money)
    |> Map.put(:uuid, uuid)
    |> Map.put(:numeric_id, numeric_id)

    # 更新玩家信息
    player_data = Map.get(state.players, user_id)
    updated_player = Map.merge(player_data, %{user_info: updated_user_info})

    new_state = %{state | players: Map.put(state.players, user_id, updated_player)}

    # 发送房间信息给玩家（使用数字ID）
    send_room_info(new_state, user_id, numeric_id)

    new_state
  end

  # 玩家离开房间
  @impl true
  def on_player_left(state, user_id) do
    Logger.info("🎰 [SLOT777] 玩家离开: #{user_id}")
    state
  end

  # 游戏开始
  @impl true
  def on_game_start(state) do
    Logger.info("🎰 [SLOT777] 游戏开始: #{state.id}")
    state
  end

  # 发送房间信息给玩家
  defp send_room_info(state, user_id, numeric_id) do
    # 获取玩家信息
    player = Map.get(state.players, user_id)
    player_money = get_in(player, [:user_info, :money]) || 0

    # 发送游戏配置信息（使用数字ID作为playerid）
    config_data = %{
      "difen" => state.game_data.config.difen,
      "odds" => state.game_data.config.odds_config,
      "jackpot" => state.game_data.jackpot_amount,
      "playerlist" => %{
        numeric_id => %{
          "playerid" => numeric_id,  # 客户端使用数字ID
          "money" => player_money,
          "seat" => 1
        }
      }
    }

    # 发送房间配置
    send_to_user(state, user_id, %{
      "mainId" => 4,
      "subId" => 2,  # 房间配置协议
      "data" => config_data
    })

    # 发送当前Jackpot金额
    send_to_user(state, user_id, %{
      "mainId" => 40,
      "subId" => 1005,  # SC_SLOT777_JACKPOT_P
      "data" => %{
        "jackpot" => state.game_data.jackpot_amount
      }
    })

    # 发送游戏配置信息（使用数字ID）
    money_data = %{
       "playerid" => numeric_id,  # 客户端使用数字ID
       "coin" => player_money,
    }
    # 发送当前用户余额
    send_to_user(state, user_id, %{
      "mainId" => 4,
      "subId" => 8,  # SC_SLOT777_JACKPOT_P
      "data" => money_data
    })
  end

  @doc """
  开始游戏
  """
  def start_game(room_id, user_id, odds) do
    case GenServer.call({:via, Registry, {:game_room_registry, room_id}}, {:start_game, user_id, odds}) do
      {:ok, result} -> {:ok, result}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  切换下注倍率
  """
  def switch_bet(room_id, user_id, odds) do
    case GenServer.call({:via, Registry, {:game_room_registry, room_id}}, {:switch_bet, user_id, odds}) do
      {:ok, result} -> {:ok, result}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  获取Jackpot记录
  """
  def get_jackpot_records() do
    case Slot777Jackpot.get_records() do
      {:ok, records} ->
        formatted_records = Slot777Jackpot.format_records_for_frontend(records)
        {:ok, formatted_records}
      {:error, reason} ->
        {:error, reason}
    end
  end

  # 处理游戏开始请求
  @impl true
  def handle_call({:start_game, user_id, odds}, _from, state) do
    Logger.info("🎰 [SLOT777] 玩家 #{user_id} 开始游戏，倍率: #{odds}")

    # 验证玩家是否在房间中
    if not Map.has_key?(state.players, user_id) do
      {:reply, {:error, "玩家不在房间中"}, state}
    else
      # 验证倍率是否有效
      if odds not in state.game_data.config.odds_config do
        {:reply, {:error, "无效的下注倍率"}, state}
      else
        # 获取玩家信息
        player = Map.get(state.players, user_id)
        player_uuid = get_in(player, [:user_info, :uuid])
        player_numeric_id = get_in(player, [:user_info, :numeric_id])

        # 获取玩家当前金币
        current_player_money = if player_numeric_id && player_uuid do
          # 真实玩家，从数据库重新获取最新金币
          get_user_points(player_numeric_id)
        else
          # 机器人或游客，使用房间状态中的金币
          get_in(player, [:user_info, :money]) || 0
        end

        # 计算下注金额
        bet_amount = state.game_data.config.difen * odds

        # 检查玩家余额
        if current_player_money < bet_amount do
          {:reply, {:error, "余额不足"}, state}
        else
          # 生成游戏结果
          game_result = Slot777GameLogic.generate_game_result(odds, current_player_money, state.game_data.config.difen)

          # 更新玩家金币
          final_money = current_player_money + game_result["changemoney"]

          # 如果是真实玩家，更新数据库中的积分
          if player_uuid && player_numeric_id do
            change_amount = game_result["changemoney"]
            if change_amount > 0 do
              {:ok, _} = Accounts.add_points(player_uuid, change_amount)
            else
              {:ok, _} = Accounts.subtract_points(player_uuid, abs(change_amount))
            end
            # 重新获取更新后的金币确保同步
            updated_final_money = get_user_points(player_numeric_id)
            final_money = updated_final_money
          end

          # 更新房间状态中的玩家金币
          updated_player = put_in(player, [:user_info, :money], final_money)

          # 更新房间状态
          new_players = Map.put(state.players, user_id, updated_player)
          new_game_data = %{state.game_data |
            current_round: state.game_data.current_round + 1,
            current_odds: Map.put(state.game_data.current_odds, user_id, odds)
          }

          new_state = %{state |
            players: new_players,
            game_data: new_game_data
          }

          # 处理Jackpot
          if game_result["jackpotcash"] > 0 do
            player_name = get_in(player, [:user_info, :name]) || "玩家#{player_numeric_id}"
            # 使用数字ID记录Jackpot
            Slot777Jackpot.trigger_jackpot(player_numeric_id, player_name, bet_amount, game_result["sevennum"])
          end

          # 贡献到Jackpot池
          Slot777Jackpot.contribute(bet_amount)

          # 发送游戏结果（包含最新金币信息和数字ID）
          enhanced_result = game_result
          |> Map.put("current_money", final_money)
          |> Map.put("playerid", player_numeric_id)  # 确保客户端收到数字ID

          {:reply, {:ok, enhanced_result}, new_state}
        end
      end
    end
  end

  # 处理切换下注倍率请求
  @impl true
  def handle_call({:switch_bet, user_id, odds}, _from, state) do
    Logger.info("🎰 [SLOT777] 玩家 #{user_id} 切换下注倍率: #{odds}")

    # 验证玩家是否在房间中
    if not Map.has_key?(state.players, user_id) do
      {:reply, {:error, "玩家不在房间中"}, state}
    else
      # 验证倍率是否有效
      if odds not in state.game_data.config.odds_config do
        {:reply, {:error, "无效的下注倍率"}, state}
      else
        # 更新玩家当前倍率
        new_current_odds = Map.put(state.game_data.current_odds, user_id, odds)
        new_game_data = %{state.game_data | current_odds: new_current_odds}
        new_state = %{state | game_data: new_game_data}

        result = %{
          "code" => 0,
          "msg" => "切换下注倍率成功",
          "odds" => odds,
          "bet_amount" => state.game_data.config.difen * odds
        }

        {:reply, {:ok, result}, new_state}
      end
    end
  end
end
