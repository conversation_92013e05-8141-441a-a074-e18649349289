defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.Slot777.Slot777Room do
  @moduledoc """
  Slot777游戏房间实现
  """

  use <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.RoomBase, game_type: :slot777
  require Logger

  alias <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.RoomManager
  alias CypridinaWeb.GameChannel
  alias Cypridina.Teen.GameSystem.Games.Slot777.Slot777GameLogic
  alias <PERSON>pridina.Teen.GameSystem.Games.Slot777.Slot777Jackpot
  alias Cypridina.Accounts.User
  alias Cypridina.Accounts

  # 用户信息获取辅助函数
  @doc """
  根据数字ID获取用户的UUID和相关信息
  直接使用数据库中的numeric_id，不进行转换
  返回 {uuid, numeric_id} 或 {:error, reason}
  """
  defp get_user_info(numeric_id) when is_integer(numeric_id) do
    Logger.info("🎰 [SLOT777_ROOM] 查询用户信息 - numeric_id: #{inspect(numeric_id)}")
    # 直接使用数字ID查询用户信息
    case User.get_by_numeric_id(numeric_id) do
      {:ok, user} ->
        Logger.info("🎰 [SLOT777_ROOM] 找到用户 - UUID: #{user.id}, numeric_id: #{user.numeric_id}")
        {user.id, user.numeric_id}
      {:error, reason} ->
        Logger.error("🎰 [SLOT777_ROOM] 用户查询失败 - numeric_id: #{numeric_id}, 原因: #{inspect(reason)}")
        {:error, "用户不存在"}
    end
  end

  defp get_user_info(invalid_id) do
    type_info = if is_struct(invalid_id) do
      invalid_id.__struct__
    else
      :primitive
    end
    Logger.error("🎰 [SLOT777_ROOM] 无效的用户ID类型 - 值: #{inspect(invalid_id)}, 类型: #{inspect(type_info)}")
    {:error, "无效的用户ID"}
  end

  @doc """
  根据数字ID获取用户的真实积分
  """
  defp get_user_points(numeric_id) when is_integer(numeric_id) do
    case User.get_by_numeric_id(numeric_id) do
      {:ok, user} ->
        case Accounts.get_user_points(user.id) do
          points when is_integer(points) -> points
          _ -> 1_000_000  # 默认积分
        end
      {:error, _} -> 1_000_000  # 默认积分
    end
  end

  defp get_user_points(_), do: 1_000_000

  @doc """
  根据UUID获取用户的数字ID
  返回 {:ok, numeric_id} 或 {:error, reason}
  """
  defp get_numeric_id_by_uuid(uuid) when is_binary(uuid) do
    Logger.info("🎰 [SLOT777_ROOM] 根据UUID查询数字ID - UUID: #{inspect(uuid)}")

    try do
      # 使用Ash框架的read方法通过UUID查询用户
      case Ash.get(User, uuid) do
        {:ok, user} ->
          Logger.info("🎰 [SLOT777_ROOM] 找到用户 - UUID: #{user.id}, numeric_id: #{user.numeric_id}")
          {:ok, user.numeric_id}
        {:error, %Ash.Error.Query.NotFound{}} ->
          Logger.error("🎰 [SLOT777_ROOM] 用户不存在 - UUID: #{uuid}")
          {:error, "用户不存在"}
        {:error, reason} ->
          Logger.error("🎰 [SLOT777_ROOM] UUID查询失败 - UUID: #{uuid}, 原因: #{inspect(reason)}")
          {:error, "查询失败"}
      end
    rescue
      error ->
        Logger.error("🎰 [SLOT777_ROOM] UUID查询异常 - UUID: #{uuid}, 异常: #{inspect(error)}")
        {:error, "查询异常"}
    end
  end

  defp get_numeric_id_by_uuid(invalid_uuid) do
    Logger.error("🎰 [SLOT777_ROOM] 无效的UUID格式 - 值: #{inspect(invalid_uuid)}")
    {:error, "无效的UUID格式"}
  end

  @doc """
  根据用户ID（可能是UUID或数字ID）获取数字ID
  支持自动识别输入类型并返回对应的数字ID
  返回 {:ok, numeric_id} 或 {:error, reason}
  """
  def get_numeric_id(user_id) when is_binary(user_id) do
    # 如果是字符串，尝试作为UUID处理
    get_numeric_id_by_uuid(user_id)
  end

  def get_numeric_id(user_id) when is_integer(user_id) do
    # 如果已经是数字，直接返回
    {:ok, user_id}
  end

  def get_numeric_id(invalid_id) do
    Logger.error("🎰 [SLOT777_ROOM] 无效的用户ID类型 - 值: #{inspect(invalid_id)}")
    {:error, "无效的用户ID类型"}
  end

  # 初始化游戏逻辑
  @impl true
  def init_game_logic(state) do
    Logger.info("🎰 [SLOT777] 初始化游戏房间: #{state.id}")
    game_config = %{
      # 最小下注
      min_bet: Map.get(state.config, :min_bet, 10),
      # 最大下注
      max_bet: Map.get(state.config, :max_bet, 10000),
      # 赔率配置
      odds_config: Map.get(state.config, :odds_config, [1, 2, 5, 10, 20, 50, 100]),
      # 底分
      difen: 10
    }

    game_data = %{
      # 游戏配置
      config: game_config,
      # 当前状态
      status: :waiting,
      # 玩家下注记录
      bets: %{},
      # 当前玩家下注倍率
      current_odds: %{},
      # 游戏结果
      results: [],
      # 当前回合
      current_round: 0,
      # 免费游戏状态
      free_games: %{},
      # Jackpot池
      jackpot_amount: Slot777Jackpot.get_current_amount()
    }

    %{state | game_data: game_data}
  end

  # 玩家加入房间后的处理
  @impl true
  def on_player_joined(state, user_id, user_info) do
    Logger.info("🎰 [SLOT777] 玩家加入: #{user_id}")

    # 获取玩家真实积分，slot777玩家初始金币为1000000
    updated_user_info = if is_integer(user_id) and user_id < 0 do
      # 机器人使用随机积分
      robot_money = 10000 + :rand.uniform(50000)
      Map.put(user_info, :money, robot_money)
    else
      # 真实玩家获取UserAsset中的积分，但slot777玩家初始金币为1000000
      real_points = Cypridina.Accounts.get_user_points(user_id)
      # 如果玩家积分为0或很少，给予初始金币
      initial_money = if real_points < 1000000, do: 1000000, else: real_points
      Map.put(user_info, :money, initial_money)
    end

    # 更新state中的玩家信息
    new_state = update_player_info(state, user_id, updated_user_info)
    player_list = new_state.players
    |> Enum.map(fn {player_id, player_data} ->
      player_money = get_in(player_data, [:user_info, :money]) || 0
      player_name = get_in(player_data, [:user_info, :name]) || "玩家#{player_id}"

      %{
        "playerid" => player_id,
        "money" => player_money,
        "name" => player_name,
        "seat" => 1
      }
    end)
    # 发送加入房间成功响应
    response = %{
      "mainId" => 4,
      "subId" => 2,  # SC_SLOT777_JOIN_ROOM_P
      "data" => %{
        "code" => 0,
        "msg" => "加入房间成功",
        "playerlist"=> player_list
      }
    }
    send_to_user(new_state, user_id, response)
    # 发送游戏配置给新玩家
   # send_game_config(new_state, user_id)

    # 发送当前游戏状态给新玩家
    #send_game_state_to_user(new_state, user_id)

    # 发送玩家列表给新加入的玩家
    #send_player_list_to_user(new_state, user_id)

    # 广播玩家数量变化通知
    broadcast_player_count_change(new_state)

    new_state
  end

  # 玩家重连加入房间
  @impl true
  def on_player_rejoined(state, user_id, user_info) do
    Logger.info("🎰 [SLOT777] 玩家重连加入: #{user_id}")

    # 发送游戏配置给重连玩家
    send_game_config(state, user_id)

    # 发送当前游戏状态给重连玩家
    send_game_state_to_user(state, user_id)

    # 发送玩家列表给重连玩家
    send_player_list_to_user(state, user_id)

    state
  end

  # 玩家离开房间
  @impl true
  def on_player_left(state, user_id) do
    Logger.info("🎰 [SLOT777] 玩家离开: #{user_id}")

    # 广播玩家数量变化通知
    broadcast_player_count_change(state)

    state
  end

  # 游戏开始
  @impl true
  def on_game_start(state) do
    Logger.info("🎰 [SLOT777] 游戏开始: #{state.id}")
    state
  end

  # 处理游戏消息
  @impl true
  def handle_game_message(state, user_id, message) do
    Logger.info("🎰 [SLOT777] 收到游戏消息 - 房间: #{state.id}, 用户: #{user_id}, 消息: #{inspect(message)}")

    case message do
      # 处理MainID=40的Slot777协议消息
      %{"mainId" => 40, "subId" => sub_id, "data" => data} ->
        handle_slot777_protocol(state, user_id, sub_id, data)

      # 兼容性处理
      %{"cmd" => "get_room_info"} ->
        Logger.info("🏠 [ROOM_INFO_REQUEST] 房间信息请求 - 用户: #{user_id}")
        # 发送房间信息给用户
        send_game_config(state, user_id)
        send_game_state_to_user(state, user_id)
        state

      %{"cmd" => "request_jackpot"} ->
        Logger.info("🎰 [JACKPOT] 请求奖池信息 - 用户: #{user_id}")
        handle_request_jackpot(state, user_id)

      _ ->
        Logger.info("ℹ️ [GAME_MESSAGE] 消息已通过客户端协议处理或为未知消息 - 用户: #{user_id}, 消息: #{inspect(message)}")
        state
    end
  end

  # 处理Slot777协议消息
  defp handle_slot777_protocol(state, user_id, sub_id, data) do
    Logger.info("🎰 [SLOT777_PROTOCOL] 处理协议 - SubID: #{sub_id}, 用户: #{user_id}, 数据: #{inspect(data)}")

    case sub_id do
      # 加入房间 (CS_SLOT777_JOIN_ROOM_P)
      1 ->
        handle_join_room_request(state, user_id, data)

      # 离开房间 (CS_SLOT777_LEAVE_ROOM_P)
      2 ->
        handle_leave_room_request(state, user_id, data)

      # 游戏开始 (CS_SLOT777_GAMESTART_P)
      1000 ->
        handle_game_start_request(state, user_id, data)

      # Jackpot记录 (CS_SLOT777_JPLIST_P)
      1003 ->
        handle_jackpot_list_request(state, user_id, data)

      # 切换下注倍率 (CS_SLOTS_SWITCH_BET_P)
      1009 ->
        handle_switch_bet_request(state, user_id, data)

      # 获取房间信息
      1006 ->
        handle_room_info_request(state, user_id, data)

      # 其他未实现的协议
      _ ->
        Logger.warning("🎰 [SLOT777] 未实现的子协议: #{sub_id}")
        send_error_response(state, user_id, sub_id, "未实现的协议")
        state
    end
  end

  # 发送游戏配置给玩家
  defp send_game_config(state, user_id) do
    Logger.info("⚙️ [SEND_CONFIG] 发送游戏配置 - 用户: #{user_id}, 房间: #{state.id}")

    # 获取玩家信息
    player = Map.get(state.players, user_id)
    player_money = get_in(player, [:user_info, :money]) || 1000000

    message = %{
      "mainId" => 4,     # MainProto.Slot777
      "subId" => 2,       # 游戏配置协议
      "data" => %{
        # 底分配置
        "difen" => state.game_data.config.difen,
        # 下注倍率配置
        "odds" => state.game_data.config.odds_config,
        # 下注限制
        "BetMax" => state.game_data.config.max_bet,
        "BetNeed" => state.game_data.config.min_bet,
        # 当前Jackpot金额
        "jackpot" => state.game_data.jackpot_amount,
        # 玩家金币
        "money" => player_money,
        # 当前回合ID
        "roundid" => state.game_data.current_round
      }
    }

    Logger.info("📤 [SEND_CONFIG] 配置消息内容 - 协议: 40/2, 底分: #{state.game_data.config.difen}")
    send_to_user(state, user_id, message)
    # Logger.info("✅ [SEND_CONFIG] 游戏配置已发送:#{get_numeric_id(user_id)}")
  end

  # 发送游戏状态给指定用户
  defp send_game_state_to_user(state, user_id) do
    # 获取玩家信息
    player = Map.get(state.players, user_id)
    player_money = get_in(player, [:user_info, :money]) || 1000000

    message = %{
      "mainId" => 40,     # MainProto.Slot777
      "subId" => 3,       # 游戏状态协议
      "data" => %{
        "status" => state.game_data.status,
        "round" => state.game_data.current_round,
        "money" => player_money,
        "jackpot" => state.game_data.jackpot_amount
      }
    }

    Logger.info("📤 [SEND_GAME_STATE] 发送游戏状态给用户 - 用户: #{user_id}, 状态: #{state.game_data.status}")
    send_to_user(state, user_id, message)
  end

  # 发送玩家列表给指定用户
  defp send_player_list_to_user(state, user_id) do
    # 构建玩家列表
    player_list = state.players
    |> Enum.map(fn {player_id, player_data} ->
      player_money = get_in(player_data, [:user_info, :money]) || 0
      player_name = get_in(player_data, [:user_info, :name]) || "玩家#{player_id}"

      %{
        "playerid" => player_id,
        "money" => player_money,
        "name" => player_name,
        "seat" => 1
      }
    end)

    message = %{
      "mainId" => 40,     # MainProto.Slot777
      "subId" => 1007,    # SC_SLOT777_PLAYERLIST_P
      "data" => %{
        "playerlist" => player_list,
        "totalplayernum" => map_size(state.players)
      }
    }

    Logger.info("📤 [SEND_PLAYER_LIST] 发送玩家列表给用户 - 用户: #{user_id}")
    send_to_user(state, user_id, message)
  end

  # 广播玩家数量变化通知
  defp broadcast_player_count_change(state) do
    message = %{
      "mainId" => 40,     # MainProto.Slot777
      "subId" => 1007,    # SC_SLOT777_PLAYERLIST_P
      "data" => %{
        "totalplayernum" => map_size(state.players)
      }
    }

    Logger.info("📤 [BROADCAST_PLAYER_COUNT] 广播玩家数量变化 - 房间总人数: #{map_size(state.players)}")
    broadcast_to_room(state, message)
  end

  # 更新玩家信息
  defp update_player_info(state, user_id, updated_user_info) do
    case Map.get(state.players, user_id) do
      nil -> state
      player ->
        updated_player = %{player | user_info: updated_user_info}
        updated_players = Map.put(state.players, user_id, updated_player)
        %{state | players: updated_players}
    end
  end

  # 处理请求奖池信息
  defp handle_request_jackpot(state, user_id) do
    Logger.info("🎰 [REQUEST_JACKPOT] 处理请求奖池信息 - 用户: #{user_id}")

    # 发送当前Jackpot金额
    send_to_user(state, user_id, %{
      "mainId" => 40,
      "subId" => 1005,  # SC_SLOT777_JACKPOT_P
      "data" => %{
        "jackpot" => state.game_data.jackpot_amount
      }
    })

    state
  end

  @doc """
  开始游戏
  """
  def start_game(room_id, user_id, odds) do
    case GenServer.call({:via, Registry, {:game_room_registry, room_id}}, {:start_game, user_id, odds}) do
      {:ok, result} -> {:ok, result}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  切换下注倍率
  """
  def switch_bet(room_id, user_id, odds) do
    case GenServer.call({:via, Registry, {:game_room_registry, room_id}}, {:switch_bet, user_id, odds}) do
      {:ok, result} -> {:ok, result}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  获取Jackpot记录
  """
  def get_jackpot_records() do
    case Slot777Jackpot.get_records() do
      {:ok, records} ->
        formatted_records = Slot777Jackpot.format_records_for_frontend(records)
        {:ok, formatted_records}
      {:error, reason} ->
        {:error, reason}
    end
  end

  # 处理游戏开始请求
  @impl true
  def handle_call({:start_game, user_id, odds}, _from, state) do
    Logger.info("🎰 [SLOT777] 玩家 #{user_id} 开始游戏，倍率: #{odds}")

    # 验证玩家是否在房间中
    if not Map.has_key?(state.players, user_id) do
      {:reply, {:error, "玩家不在房间中"}, state}
    else
      # 验证倍率是否有效
      if odds not in state.game_data.config.odds_config do
        {:reply, {:error, "无效的下注倍率"}, state}
      else
        # 获取玩家信息
        player = Map.get(state.players, user_id)
        player_uuid = get_in(player, [:user_info, :uuid])
        player_numeric_id = get_in(player, [:user_info, :numeric_id])

        # 获取玩家当前金币
        current_player_money = if player_numeric_id && player_uuid do
          # 真实玩家，从数据库重新获取最新金币
          get_user_points(player_numeric_id)
        else
          # 机器人或游客，使用房间状态中的金币
          get_in(player, [:user_info, :money]) || 0
        end

        # 计算下注金额
        bet_amount = state.game_data.config.difen * odds

        # 检查玩家余额
        if current_player_money < bet_amount do
          {:reply, {:error, "余额不足"}, state}
        else
          # 生成游戏结果
          game_result = Slot777GameLogic.generate_game_result(odds, current_player_money, state.game_data.config.difen)

          # 更新玩家金币
          final_money = current_player_money + game_result["changemoney"]

          # 如果是真实玩家，更新数据库中的积分
          if player_uuid && player_numeric_id do
            change_amount = game_result["changemoney"]
            if change_amount > 0 do
              {:ok, _} = Accounts.add_points(player_uuid, change_amount)
            else
              {:ok, _} = Accounts.subtract_points(player_uuid, abs(change_amount))
            end
            # 重新获取更新后的金币确保同步
            updated_final_money = get_user_points(player_numeric_id)
            final_money = updated_final_money
          end

          # 更新房间状态中的玩家金币
          updated_player = put_in(player, [:user_info, :money], final_money)

          # 更新房间状态
          new_players = Map.put(state.players, user_id, updated_player)
          new_game_data = %{state.game_data |
            current_round: state.game_data.current_round + 1,
            current_odds: Map.put(state.game_data.current_odds, user_id, odds)
          }

          new_state = %{state |
            players: new_players,
            game_data: new_game_data
          }

          # 处理Jackpot
          if game_result["jackpotcash"] > 0 do
            player_name = get_in(player, [:user_info, :name]) || "玩家#{player_numeric_id}"
            # 使用数字ID记录Jackpot
            Slot777Jackpot.trigger_jackpot(player_numeric_id, player_name, bet_amount, game_result["sevennum"])
          end

          # 贡献到Jackpot池
          Slot777Jackpot.contribute(bet_amount)

          # 发送游戏结果（包含最新金币信息和数字ID）
          enhanced_result = game_result
          |> Map.put("current_money", final_money)
          |> Map.put("playerid", player_numeric_id)  # 确保客户端收到数字ID

          {:reply, {:ok, enhanced_result}, new_state}
        end
      end
    end
  end

  # 处理切换下注倍率请求
  @impl true
  def handle_call({:switch_bet, user_id, odds}, _from, state) do
    Logger.info("🎰 [SLOT777] 玩家 #{user_id} 切换下注倍率: #{odds}")

    # 验证玩家是否在房间中
    if not Map.has_key?(state.players, user_id) do
      {:reply, {:error, "玩家不在房间中"}, state}
    else
      # 验证倍率是否有效
      if odds not in state.game_data.config.odds_config do
        {:reply, {:error, "无效的下注倍率"}, state}
      else
        # 更新玩家当前倍率
        new_current_odds = Map.put(state.game_data.current_odds, user_id, odds)
        new_game_data = %{state.game_data | current_odds: new_current_odds}
        new_state = %{state | game_data: new_game_data}

        result = %{
          "code" => 0,
          "msg" => "切换下注倍率成功",
          "odds" => odds,
          "bet_amount" => state.game_data.config.difen * odds
        }

        {:reply, {:ok, result}, new_state}
      end
    end
  end

  # ==================== Slot777协议处理函数 ====================

  # 处理加入房间请求
  defp handle_join_room_request(state, user_id, data) do
    Logger.info("🎰 [JOIN_ROOM] 处理加入房间请求 - 用户: #{user_id}, 数据: #{inspect(data)}")

    # 获取用户信息
    user_info = Map.get(data, "user_info", %{})
    user_info = Map.merge(user_info, %{
      "name" => Map.get(user_info, "name", "Player#{user_id}"),
      "money" => 1000000  # Slot777玩家初始金币
    })
     player_list = state.players
    |> Enum.map(fn {player_id, player_data} ->
      player_money = get_in(player_data, [:user_info, :money]) || 0
      player_name = get_in(player_data, [:user_info, :name]) || "玩家#{player_id}"

      %{
        "playerid" => player_id,
        "money" => player_money,
        "name" => player_name,
        "seat" => 1
      }
    end)
    # 发送加入房间成功响应
    response = %{
      "mainId" => 4,
      "subId" => 2,  # SC_SLOT777_JOIN_ROOM_P
      "data" => %{
        "code" => 0,
        "msg" => "加入房间成功",
        "room_id" => state.id,
        "is_creator" => false,
        "playerlist"=> player_list
      }
    }

    send_to_user(state, user_id, response)

    # 发送游戏配置和状态
    # send_game_config(state, user_id)
    # send_game_state_to_user(state, user_id)
    # send_player_list_to_user(state, user_id)

    state
  end

  # 处理离开房间请求
  defp handle_leave_room_request(state, user_id, data) do
    Logger.info("🎰 [LEAVE_ROOM] 处理离开房间请求 - 用户: #{user_id}, 数据: #{inspect(data)}")

    # 发送离开房间成功响应
    response = %{
      "mainId" => 40,
      "subId" => 12,  # SC_SLOT777_LEAVE_ROOM_P
      "data" => %{
        "code" => 0,
        "msg" => "离开房间成功"
      }
    }

    send_to_user(state, user_id, response)
    state
  end

  # 处理游戏开始请求
  defp handle_game_start_request(state, user_id, data) do
    Logger.info("🎰 [GAME_START] 处理游戏开始请求 - 用户: #{user_id}, 数据: #{inspect(data)}")

    odds = Map.get(data, "odds", 1)

    # 调用内部游戏开始处理
    case handle_call({:start_game, user_id, odds}, nil, state) do
      {:reply, {:ok, result}, new_state} ->
        # 发送游戏结果给玩家
        response = %{
          "mainId" => 40,
          "subId" => 1001,  # SC_SLOT777_GAMESTART_P
          "data" => result
        }
        send_to_user(new_state, user_id, response)

        # 广播游戏结果给其他玩家
        broadcast_game_result(new_state, user_id, result)

        new_state

      {:reply, {:error, reason}, state} ->
        # 发送错误消息
        send_error_response(state, user_id, 1001, reason)
        state
    end
  end

  # 处理Jackpot记录请求
  defp handle_jackpot_list_request(state, user_id, data) do
    Logger.info("🎰 [JACKPOT_LIST] 处理Jackpot记录请求 - 用户: #{user_id}, 数据: #{inspect(data)}")

    case get_jackpot_records() do
      {:ok, records} ->
        response = %{
          "mainId" => 40,
          "subId" => 1004,  # SC_SLOT777_JPLIST_P
          "data" => %{
            "records" => records
          }
        }
        send_to_user(state, user_id, response)

      {:error, reason} ->
        send_error_response(state, user_id, 1004, "获取Jackpot记录失败: #{reason}")
    end

    state
  end

  # 处理切换下注倍率请求
  defp handle_switch_bet_request(state, user_id, data) do
    Logger.info("🎰 [SWITCH_BET] 处理切换下注倍率请求 - 用户: #{user_id}, 数据: #{inspect(data)}")

    odds = Map.get(data, "odds", 1)

    # 调用内部切换倍率处理
    case handle_call({:switch_bet, user_id, odds}, nil, state) do
      {:reply, {:ok, result}, new_state} ->
        # 发送切换结果给玩家
        response = %{
          "mainId" => 40,
          "subId" => 1009,  # CS_SLOTS_SWITCH_BET_P
          "data" => result
        }
        send_to_user(new_state, user_id, response)
        new_state

      {:reply, {:error, reason}, state} ->
        send_error_response(state, user_id, 1009, reason)
        state
    end
  end

  # 处理房间信息请求
  defp handle_room_info_request(state, user_id, data) do
    Logger.info("🎰 [ROOM_INFO] 处理房间信息请求 - 用户: #{user_id}, 数据: #{inspect(data)}")

    # 发送游戏配置和状态
    send_game_config(state, user_id)
    send_game_state_to_user(state, user_id)
    send_player_list_to_user(state, user_id)

    state
  end

  # 发送错误响应
  defp send_error_response(state, user_id, sub_id, reason) do
    response = %{
      "mainId" => 40,
      "subId" => sub_id,
      "data" => %{
        "code" => 1,
        "msg" => reason
      }
    }
    send_to_user(state, user_id, response)
  end

  # 广播游戏结果给其他玩家
  defp broadcast_game_result(state, player_id, result) do
    # 获取玩家信息
    player = Map.get(state.players, player_id)
    player_name = get_in(player, [:user_info, :name]) || "玩家#{player_id}"

    # 构建广播消息
    broadcast_data = %{
      "playerid" => player_id,
      "name" => player_name,
      "result" => result,
      "timestamp" => System.system_time(:millisecond)
    }

    message = %{
      "mainId" => 40,
      "subId" => 1008,  # SC_SLOT777_GAMERESULT_P
      "data" => broadcast_data
    }

    Logger.info("📤 [BROADCAST_GAME_RESULT] 广播游戏结果 - 玩家: #{player_id}")
    broadcast_to_room(state, message, player_id)  # 排除玩家自己
  end

end
