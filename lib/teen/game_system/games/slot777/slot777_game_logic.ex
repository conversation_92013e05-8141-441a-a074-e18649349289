defmodule Cyprid<PERSON>.Teen.GameSystem.Games.Slot777.Slot777GameLogic do
  @moduledoc """
  Slot777游戏逻辑引擎
  实现老虎机的核心算法，包括：
  - 图标生成
  - 中奖线计算
  - 赔率计算
  - Jackpot计算
  - 免费游戏触发
  """

  require Logger

  # 游戏常量
  @rows 3
  @cols 5
  @max_lines 9

  # 图标类型 (对应前端 EM_SLOT777_ICONTYPE) - 值范围0-10
  @icon_types %{
    cherry: 0,      # 樱桃
    lemon: 1,       # 柠檬
    orange: 2,      # 橙子
    plum: 3,        # 李子
    bell: 4,        # 铃铛
    bar: 5,         # BAR
    seven: 6,       # 777
    diamond: 7,     # 钻石
    star: 8,        # 星星
    crown: 9,       # 皇冠
    wild: 10        # 万能牌
  }

  # 图标权重配置 (数字越大出现概率越高) - 包含0-10
  @icon_weights %{
    0 => 25,   # 樱桃 - 高频
    1 => 20,   # 柠檬 - 高频
    2 => 18,   # 橙子 - 中频
    3 => 15,   # 李子 - 中频
    4 => 12,   # 铃铛 - 中频
    5 => 8,    # BAR - 低频
    6 => 3,    # 777 - 极低频 (Jack<PERSON>)
    7 => 6,    # 钻石 - 低频
    8 => 5,    # 星星 - 低频 (免费游戏触发)
    9 => 4,    # 皇冠 - 低频
    10 => 2    # 万能牌 - 极低频
  }

  # 中奖线定义 (3行5列，按位置索引1-15)
  @win_lines [
    [2, 7, 12],                    # 线1: 中间行
    [1, 6, 11],                    # 线2: 上行
    [3, 8, 13],                    # 线3: 下行
    [1, 7, 13],                    # 线4: 对角线1
    [3, 7, 11],                    # 线5: 对角线2
    [2, 6, 12],                    # 线6: 中上中
    [2, 8, 12],                    # 线7: 中下中
    [1, 6, 11, 16, 21],           # 线8: 上行全线
    [3, 8, 13, 18, 23]            # 线9: 下行全线
  ]

  # 赔率表 (图标类型 => {连击数 => 赔率}) - 包含0-10
  @payout_table %{
    0 => %{3 => 5, 4 => 15, 5 => 50},      # 樱桃
    1 => %{3 => 8, 4 => 20, 5 => 80},      # 柠檬
    2 => %{3 => 10, 4 => 25, 5 => 100},    # 橙子
    3 => %{3 => 12, 4 => 30, 5 => 120},    # 李子
    4 => %{3 => 15, 4 => 40, 5 => 150},    # 铃铛
    5 => %{3 => 20, 4 => 60, 5 => 200},    # BAR
    6 => %{3 => 500, 4 => 2000, 5 => 10000}, # 777 (Jackpot触发)
    7 => %{3 => 25, 4 => 80, 5 => 300},    # 钻石
    8 => %{3 => 30, 4 => 100, 5 => 400},   # 星星 (免费游戏触发)
    9 => %{3 => 40, 4 => 150, 5 => 600},   # 皇冠
    10 => %{3 => 50, 4 => 200, 5 => 1000}  # 万能牌
  }

  @doc """
  生成游戏结果
  """
  def generate_game_result(bet_odds, player_money, base_bet \\ 10) do
    # 生成3x5的图标矩阵
    icon_matrix = generate_icon_matrix()

    # 转换为前端期望的一维数组格式 (1-15)
    icon_result = matrix_to_array(icon_matrix)

    # 计算中奖线
    {win_lines, total_multiplier} = calculate_win_lines(icon_matrix)

    # 计算实际下注金额
    bet_amount = base_bet * bet_odds

    # 计算奖金
    win_money = if total_multiplier > 0 do
      trunc(bet_amount * total_multiplier / 10)
    else
      0
    end

    # 计算玩家输赢 (赢金 - 下注)
    change_money = win_money - bet_amount

    # 检查是否触发Jackpot
    {jackpot_cash, seven_count} = calculate_jackpot(icon_matrix, bet_amount)

    # 检查是否触发免费游戏
    free_times = calculate_free_times(icon_matrix)
    # free_times=10
    # 构建结果
    %{
      "freetimes" => free_times,
      "sevennum" => seven_count,
      "iconresult" => icon_result,
      "linecount" => length(win_lines),
      "lineresult" => win_lines,
      "totalmult" => total_multiplier,
      "winmoney" => win_money,
      "changemoney" => change_money,
      "jackpotcash" => jackpot_cash,
      "luckyjackpot" => 0  # 转盘彩金，暂时为0
    }
  end

  @doc """
  生成3x5的图标矩阵
  """
  defp generate_icon_matrix() do
    for _row <- 1..@rows do
      for _col <- 1..@cols do
        generate_weighted_icon()
      end
    end
  end

  @doc """
  根据权重生成图标
  """
  defp generate_weighted_icon() do
    total_weight = @icon_weights |> Map.values() |> Enum.sum()
    random_value = :rand.uniform(total_weight)

    find_icon_by_weight(random_value, @icon_weights, 0)
  end

  defp find_icon_by_weight(target, weights, current_sum) do
    Enum.find_value(weights, fn {icon, weight} ->
      new_sum = current_sum + weight
      if target <= new_sum do
        icon
      else
        find_icon_by_weight(target, Map.delete(weights, icon), new_sum)
      end
    end) || 0  # 默认返回樱桃（现在是值0）
  end

  @doc """
  将3x5矩阵转换为1-15的一维数组
  """
  defp matrix_to_array(matrix) do
    matrix
    |> List.flatten()
    |> Enum.with_index(1)
    |> Enum.into(%{}, fn {icon, index} -> {index, icon} end)
  end

  @doc """
  计算中奖线
  """
  defp calculate_win_lines(matrix) do
    flat_matrix = List.flatten(matrix)

    win_lines =
      @win_lines
      |> Enum.with_index(1)
      |> Enum.reduce([], fn {line_positions, line_index}, acc ->
        case check_line_win(flat_matrix, line_positions) do
          {true, icon_type, count, multiplier} ->
            win_info = %{
              "line" => line_index,
              "icon" => icon_type,
              "num" => count,
              "mult" => multiplier
            }
            [win_info | acc]

          {false, _, _, _} ->
            acc
        end
      end)
      |> Enum.reverse()

    total_multiplier =
      win_lines
      |> Enum.map(& &1["mult"])
      |> Enum.sum()

    {win_lines, total_multiplier}
  end

  @doc """
  检查单条线是否中奖
  """
  defp check_line_win(flat_matrix, line_positions) do
    # 获取线上的图标
    line_icons = Enum.map(line_positions, fn pos ->
      Enum.at(flat_matrix, pos - 1)
    end)

    # 检查连续相同图标
    case find_consecutive_match(line_icons) do
      {icon_type, count} when count >= 3 ->
        multiplier = get_payout_multiplier(icon_type, count)
        {true, icon_type, count, multiplier}

      _ ->
        {false, nil, 0, 0}
    end
  end

  @doc """
  查找连续匹配的图标
  """
  defp find_consecutive_match([first | rest]) do
    count = 1 + count_consecutive(first, rest)
    {first, count}
  end

  defp count_consecutive(_target, []), do: 0
  defp count_consecutive(target, [target | rest]), do: 1 + count_consecutive(target, rest)
  defp count_consecutive(_target, _), do: 0

  @doc """
  获取赔率倍数
  """
  defp get_payout_multiplier(icon_type, count) do
    @payout_table
    |> Map.get(icon_type, %{})
    |> Map.get(count, 0)
  end

  @doc """
  计算Jackpot
  """
  defp calculate_jackpot(matrix, bet_amount) do
    flat_matrix = List.flatten(matrix)
    seven_count = Enum.count(flat_matrix, & &1 == 6)  # 777现在是值6

    jackpot_cash = cond do
      seven_count >= 5 -> bet_amount * 1000  # 5个7 = 1000倍
      seven_count >= 4 -> bet_amount * 200   # 4个7 = 200倍
      seven_count >= 3 -> bet_amount * 50    # 3个7 = 50倍
      true -> 0
    end

    {jackpot_cash, seven_count}
  end

  @doc """
  计算免费游戏次数
  """
  defp calculate_free_times(matrix) do
    flat_matrix = List.flatten(matrix)
    star_count = Enum.count(flat_matrix, & &1 == 8)  # 星星现在是值8，触发免费游戏

    cond do
      star_count >= 5 -> 20  # 5个星星 = 20次免费
      star_count >= 4 -> 15  # 4个星星 = 15次免费
      star_count >= 3 -> 10  # 3个星星 = 10次免费
      true -> 0
    end
  end

  @doc """
  获取当前Jackpot池金额
  """
  def get_current_jackpot() do
    # 这里可以从数据库或缓存中获取实际的Jackpot金额
    # 暂时返回一个模拟值
    base_jackpot = 100000
    random_addition = :rand.uniform(50000)
    base_jackpot + random_addition
  end

  @doc """
  更新Jackpot池
  """
  def update_jackpot_pool(bet_amount) do
    # 每次下注的一定比例进入Jackpot池
    contribution = trunc(bet_amount * 0.01)  # 1%进入奖池

    # 这里应该更新数据库中的Jackpot金额
    Logger.info("🎰 [SLOT777] Jackpot池增加: #{contribution}")

    contribution
  end
end
