defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.Slot777.Slot777Game do
  @moduledoc """
  Slot777老虎机游戏定义模块

  实现游戏工厂行为，定义Slot777游戏的基本信息和配置
  """

  @behaviour Cypridina.RoomSystem.GameFactory

  @impl true
  def game_type, do: :slot777

  @impl true
  def game_name, do: "Slot777老虎机"

  @impl true
  def room_module, do: <PERSON>pridina.Teen.GameSystem.Games.Slot777.Slot777Room

  @impl true
  def default_config do
    %{
      # 最大玩家数
      max_players: 50,
      # 最小玩家数 (单人游戏)
      min_players: 1,
      # 1秒后自动开始
      auto_start_delay: 1000,
      # 启用机器人
      enable_robots: false,
      robot_count: 0,
      # 游戏配置
      game_config: %{
        # 转轮配置
        reels: 5,
        rows: 3,
        # 支付线数
        paylines: 9,
        # 最小下注
        min_bet: 1,
        # 最大下注
        max_bet: 1000,
        # 下注倍率选项
        bet_multipliers: [1, 5, 10, 50, 100, 500, 1000],
        # RTP (Return to Player) 返还率
        rtp: 96.5,
        # Jackpot配置
        jackpot_config: %{
          # Jackpot种子金额
          seed_amount: 10000,
          # 贡献率 (每次下注的百分比贡献给Jackpot)
          contribution_rate: 0.01,
          # 最小触发金额
          min_trigger_amount: 50000
        },
        # 免费游戏配置
        free_game_config: %{
          # 触发免费游戏需要的scatter数量
          scatter_count: 3,
          # 免费游戏次数
          free_spins: 10,
          # 免费游戏倍率
          multiplier: 2
        },
        # 图标配置
        symbol_config: %{
          # 普通图标
          normal_symbols: [1, 2, 3, 4, 5, 6, 7, 8, 9],
          # Wild图标
          wild_symbol: 10,
          # Scatter图标
          scatter_symbol: 11
        },
        # 支付表 (图标ID => [3连, 4连, 5连]的倍率)
        paytable: %{
          1 => [5, 25, 100],    # 樱桃
          2 => [10, 50, 200],   # 柠檬
          3 => [15, 75, 300],   # 橙子
          4 => [20, 100, 400],  # 李子
          5 => [25, 125, 500],  # 葡萄
          6 => [50, 250, 1000], # 铃铛
          7 => [100, 500, 2000], # 七
          8 => [200, 1000, 5000], # BAR
          9 => [500, 2500, 10000], # 钻石
          10 => [1000, 5000, 20000], # Wild
          11 => [0, 0, 0]       # Scatter (不参与普通支付)
        }
      }
    }
  end

  @impl true
  def is_lobby_game?, do: true

  @impl true
  def supported_game_ids do
    [
      # Slot777 (统一使用的ID)
      40
    ]
  end

  @doc """
  获取游戏统计信息
  """
  def get_game_stats do
    %{
      total_rooms: get_total_rooms(),
      active_players: get_active_players(),
      total_jackpot: get_total_jackpot()
    }
  end

  @doc """
  获取当前活跃房间数
  """
  def get_total_rooms do
    # 这里可以实现获取活跃房间数的逻辑
    0
  end

  @doc """
  获取当前活跃玩家数
  """
  def get_active_players do
    # 这里可以实现获取活跃玩家数的逻辑
    0
  end

  @doc """
  获取当前总Jackpot金额
  """
  def get_total_jackpot do
    # 这里可以实现获取总Jackpot金额的逻辑
    config = default_config()
    config.game_config.jackpot_config.seed_amount
  end

  @doc """
  验证游戏配置
  """
  def validate_config(config) do
    required_keys = [:max_players, :min_players, :game_config]

    case Enum.all?(required_keys, &Map.has_key?(config, &1)) do
      true -> {:ok, config}
      false -> {:error, "Missing required configuration keys"}
    end
  end

  @doc """
  获取游戏版本信息
  """
  def version_info do
    %{
      version: "1.0.0",
      build_date: "2024-01-01",
      features: [
        "基础老虎机游戏",
        "Jackpot系统",
        "免费游戏",
        "Wild和Scatter图标",
        "多倍率下注"
      ]
    }
  end
end
