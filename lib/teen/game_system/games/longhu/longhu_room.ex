defmodule Cypridina.Teen.GameSystem.Games.LongHu.LongHuRoom do
  @moduledoc """
  龙虎斗游戏房间实现

  游戏规则：
  - 龙虎斗是一种简单的纸牌比大小游戏
  - 分为龙、虎、和三个下注区域
  - 每局发两张牌，比较大小决定胜负
  - 支持庄家系统和机器人
  """

  use Cypridina.Teen.GameSystem.RoomBase, game_type: :longhu
  import Bitwise

  alias Cypridina.Teen.GameSystem.Games.LongHu.{LongHuLogic, LongHuAI}
  alias Cypridina.Teen.GameSystem.RobotService
  alias Cypridina.Accounts
  alias CypridinaWeb.GameChannel

  # 游戏状态
  @game_phases %{
    # 等待开始
    waiting: :waiting,
    # 下注阶段
    betting: :betting,
    # 发牌阶段
    dealing: :dealing,
    # 亮牌阶段
    revealing: :revealing,
    # 结算阶段
    settling: :settling
  }

  # 下注区域
  @bet_areas %{
    # 龙
    long: 1,
    # 虎
    hu: 2,
    # 和
    he: 3
  }

  # 赔率配置 - 使用整数避免浮点数计算问题
  @odds %{
    # 龙 1:1 (实际赔付2倍，包含本金)
    long: 2,
    # 虎 1:1 (实际赔付2倍，包含本金)
    hu: 2,
    # 和 1:8 (实际赔付9倍，包含本金)
    he: 9
  }

  # ==================== 用户ID转换辅助函数 ====================

  @doc """
  将用户ID转换为客户端期望的 numeric_id 格式

  ## 参数
  - user_id: 内部用户ID

  ## 返回
  用于客户端的 numeric_id 值
  """
  defp to_numeric_id(user_id) do
    user_id  # 直接返回用户ID作为 numeric_id
  end

  @doc """
  将用户ID转换为 playerid key 格式

  ## 参数
  - user_id: 内部用户ID

  ## 返回
  用作 key 的 playerid 字符串
  """
  defp to_playerid_key(user_id) do
    "#{user_id}"  # 转换为字符串作为 key
  end

  @impl true
  def init_game_logic(state) do

    game_config = %{
      # 下注时间(秒)
      bet_time: Map.get(state.config, :bet_time, 15),
      # 发牌时间(秒)
      deal_time: Map.get(state.config, :deal_time, 3),
      # 亮牌时间(秒) - 增加到10秒让玩家有更多时间观看
      reveal_time: Map.get(state.config, :reveal_time, 10),
      # 结算时间(秒)
      settle_time: Map.get(state.config, :settle_time, 3),
      # 最小下注
      min_bet: Map.get(state.config, :min_bet, 10),
      # 最大下注 (单次)
      max_bet: Map.get(state.config, :max_bet, 10000),
      # 个人总下注限制
      max_total_bet: Map.get(state.config, :max_total_bet, 50000),
      # 区域下注限制
      area_bet_limits: %{
        long: Map.get(state.config, :area_long_limit, 100000),
        hu: Map.get(state.config, :area_hu_limit, 100000),
        he: Map.get(state.config, :area_he_limit, 50000)
      },
      # 启用机器人
      enable_robots: Map.get(state.config, :enable_robots, true),
      # 机器人数量 - 增加到15个机器人
      robot_count: Map.get(state.config, :robot_count, 15)
    }

    game_data = %{
      phase: @game_phases.betting,  # 直接开始下注阶段
      round: 1,  # 从第1轮开始
      cards: %{long: nil, hu: nil},
      # %{playerid => %{area => amount}}
      bets: %{},
      total_bets: %{long: 0, hu: 0, he: 0},
      # 庄家系统
      banker: %{
        user_id: nil,           # 当前庄家ID (nil表示系统庄家)
        name: "系统庄家",        # 庄家名称
        money: 1000000,         # 庄家金币
        turns_played: 0,        # 已坐庄局数
        max_turns: 20,          # 最大坐庄局数
        min_turns: 5,           # 最小坐庄局数
        is_system: true         # 是否系统庄家
      },
      banker_queue: [],         # 上庄队列 [%{user_id, name, money, apply_time}]
      banker_off_requests: [],  # 下庄申请列表
      # 续押系统
      last_round_bets: %{},     # 上一轮的下注记录 %{playerid => %{area => amount}}
      # 奖池系统
      jackpot: %{
        total: 1000000,         # 总奖池金额
        long: 300000,           # 龙区域奖池
        hu: 300000,             # 虎区域奖池
        he: 400000              # 和区域奖池
      },
      # 游戏统计
      statistics: %{
        total_rounds: 0,        # 总局数
        total_bets_amount: 0,   # 总下注金额
        total_players: 0,       # 总参与玩家数
        win_rates: %{           # 各区域胜率
          long: 0.0,
          hu: 0.0,
          he: 0.0
        },
        recent_results: [],     # 最近结果 (最多保存100局)
        daily_stats: %{         # 每日统计
          date: Date.utc_today(),
          rounds: 0,
          total_bets: 0,
          unique_players: MapSet.new()
        }
      },
      # 玩家个人统计 %{playerid => %{wins, losses, total_bet, profit}}
      player_stats: %{},
      # 聊天消息历史 (最多保存50条)
      chat_history: [],
      # VIP系统
      vip_benefits: %{
        level_1: %{min_bet_multiplier: 1.0, max_bet_multiplier: 1.2},
        level_2: %{min_bet_multiplier: 1.0, max_bet_multiplier: 1.5},
        level_3: %{min_bet_multiplier: 1.0, max_bet_multiplier: 2.0}
      },
      history: [],
      phase_timer: nil,
      config: game_config,
      odds: @odds
    }

    new_state = %{state | game_data: game_data}

    # 启动机器人管理定时器（每30秒检查一次）
    if game_config.enable_robots do
      Process.send_after(self(), :manage_robots, 30_000)
    end

    # 立即开始游戏循环
    start_game_loop(new_state)
  end

  @impl true
  def on_player_joined(state, user_id, user_info) do
    Logger.info("🐉 [LONGHU] 玩家加入百人场: #{user_id}")

    # 获取玩家真实积分
    updated_user_info = if is_integer(user_id) and user_id < 0 do
      # 机器人使用随机积分
      robot_money = 10000 + :rand.uniform(50000)  # 10k-60k随机积分
      Map.put(user_info, :money, robot_money)
    else
      # 真实玩家获取UserAsset中的积分
      real_points = Cypridina.Accounts.get_user_points(user_id)
      Map.put(user_info, :money, real_points)
    end

    # 更新state中的玩家信息
    new_state = update_player_info(state, user_id, updated_user_info)

    # 发送游戏配置给新玩家 (协议号3900)
    send_game_config(new_state, user_id)

    # 发送当前游戏状态给新玩家 (协议号3901)
    send_game_state_to_user(new_state, user_id)

    # 发送当前庄家信息
    send_banker_info_to_user(new_state, user_id)

    # 发送上庄列表
    send_banker_list_to_user(new_state, user_id)

    # 发送玩家列表给新加入的玩家 (协议号3921) - 默认第0页包含排行榜
    send_player_list_to_user(new_state, user_id, 0)

    # 如果启用机器人且机器人数量不足，添加机器人烘托气氛
    final_state = if new_state.game_data.config.enable_robots do
      LongHuAI.add_robots_if_needed(new_state)
    else
      new_state
    end

    # 广播玩家数量变化通知 (使用现有的3929协议)
    broadcast_player_count_change(final_state)

    final_state
  end

  @impl true
  def on_player_rejoined(state, user_id, user_info) do
    Logger.info("🐉 [LONGHU] 玩家重连加入百人场: #{user_id}")

    # 发送游戏配置给重连玩家 (协议号3900)
    send_game_config(state, user_id)

    # 发送当前游戏状态给重连玩家 (协议号3901)
    send_game_state_to_user(state, user_id)

    # 发送当前庄家信息
    send_banker_info_to_user(state, user_id)

    # 发送上庄列表
    send_banker_list_to_user(state, user_id)

    # 发送玩家列表给重连玩家 (协议号3921) - 默认第0页包含排行榜
    send_player_list_to_user(state, user_id, 0)

    state
  end

  @impl true
  def on_player_left(state, user_id) do
    # 清除玩家的下注
    new_bets = Map.delete(state.game_data.bets, user_id)

    # 重新计算总下注
    new_total_bets = calculate_total_bets(new_bets)

    game_data = %{state.game_data | bets: new_bets, total_bets: new_total_bets}

    new_state = %{state | game_data: game_data}

    # 广播玩家数量变化通知 (使用现有的3929协议)
    broadcast_player_count_change(new_state)

    new_state
  end

  @impl true
  def on_game_start(state) do
    Logger.info("🐉 [LONGHU] 开始新一轮游戏: #{state.id}")

    # 重置游戏数据
    game_data = %{
      state.game_data
      | phase: :betting,
        round: state.game_data.round + 1,
        cards: %{long: nil, hu: nil},
        bets: %{},
        total_bets: %{long: 0, hu: 0, he: 0}
    }

    new_state = %{state | game_data: game_data}

    # 启动下注阶段计时器
    new_state = start_betting_phase(new_state)

    # 启动游戏tick
    schedule_game_tick()

    new_state
  end

  @impl true
  def handle_game_message(state, user_id, message) do
    Logger.info("🎮 [GAME_MESSAGE] 收到游戏消息 - 房间: #{state.id}, 用户: #{user_id}, 消息: #{inspect(message)}")

    case message do
      # ==================== 客户端协议分发 ====================

      # XC协议 (mainId: 5) - 龙虎斗客户端协议
      %{"mainId" => 5, "subId" => sub_id} = client_message ->
        Logger.info("🐉 [CLIENT_PROTOCOL] 处理客户端协议 - SubID: #{sub_id}")
        handle_client_protocol(state, user_id, client_message)

      # ==================== 内部命令格式 ====================

      # 下注请求 (内部格式)
      %{"cmd" => "bet", "area" => area, "amount" => amount} ->
        Logger.info("🎯 [BET_REQUEST] 下注请求 - 用户: #{user_id}, 区域: #{area}, 金额: #{amount}")
        handle_bet(state, user_id, area, amount)

      # 申请上庄
      %{"cmd" => "apply_banker"} ->
        Logger.info("🏦 [BANKER_REQUEST] 申请上庄请求 - 用户: #{user_id}")
        handle_apply_banker(state, user_id)

      # 取消申请上庄
      %{"cmd" => "cancel_apply_banker"} ->
        Logger.info("🏦 [BANKER_CANCEL] 取消申请上庄请求 - 用户: #{user_id}")
        handle_cancel_apply_banker(state, user_id)

      # 申请下庄
      %{"cmd" => "apply_off_banker"} ->
        Logger.info("🏦 [BANKER_OFF] 申请下庄请求 - 用户: #{user_id}")
        handle_apply_off_banker(state, user_id)

      # 请求上庄列表
      %{"cmd" => "request_banker_list"} ->
        Logger.info("🏦 [BANKER_LIST] 请求上庄列表 - 用户: #{user_id}")
        handle_request_banker_list(state, user_id)

      # 请求玩家列表
      %{"cmd" => "request_player_list", "page" => page} ->
        Logger.info("👥 [PLAYER_LIST] 请求玩家列表 - 用户: #{user_id}, 页码: #{page}")
        handle_request_player_list(state, user_id, page)

      %{"cmd" => "request_player_list"} ->
        Logger.info("👥 [PLAYER_LIST] 请求玩家列表 - 用户: #{user_id}, 默认页码: 0")
        handle_request_player_list(state, user_id, 0)

      # 续押请求
      %{"cmd" => "follow_bet"} ->
        Logger.info("🔄 [FOLLOW_BET] 续押请求 - 用户: #{user_id}")
        handle_follow_bet(state, user_id)

      # 历史记录请求
      %{"cmd" => "get_history", "page" => page, "count" => count} ->
        Logger.info("📊 [HISTORY_REQUEST] 历史记录请求 - 用户: #{user_id}, 页码: #{page}, 数量: #{count}")
        send_history_to_user(state, user_id, page, count)
        state

      %{"cmd" => "get_history"} ->
        Logger.info("📊 [HISTORY_REQUEST] 历史记录请求 - 用户: #{user_id}, 默认参数")
        send_history_to_user(state, user_id, 0, 20)
        state

      # ==================== 房间信息和配置 ====================

      # 房间信息请求
      %{"cmd" => "get_room_info"} ->
        Logger.info("🏠 [ROOM_INFO_REQUEST] 房间信息请求 - 用户: #{user_id}")
        send_game_config(state, user_id)
        send_game_state_to_user(state, user_id)
        state

      # 奖池信息请求
      %{"cmd" => "request_jackpot"} ->
        Logger.info("🎰 [JACKPOT] 请求奖池信息 - 用户: #{user_id}")
        handle_request_jackpot(state, user_id)

      # 时间配置请求
      %{"cmd" => "request_time_config"} ->
        Logger.info("⏰ [TIME_CONFIG] 请求时间配置 - 用户: #{user_id}")
        handle_request_time_config(state, user_id)

      # ==================== 未知消息 ====================

      _ ->
        Logger.warning("❓ [UNKNOWN_MESSAGE] 未知游戏消息 - 用户: #{user_id}, 消息: #{inspect(message)}")
        state
    end
  end

  # 处理客户端协议 (mainId: 5)
  defp handle_client_protocol(state, user_id, %{"mainId" => 5, "subId" => sub_id} = message) do
    case sub_id do
      3904 ->  # CS_LHD_BUYHORSE_P - 下注请求
        Logger.info("🎯 [CLIENT_BET] 处理客户端下注请求 - 用户: #{user_id}")
        handle_client_bet(message, state, user_id)

      3906 ->  # CS_LHD_REQUEST_ZHUANG_P - 申请上庄
        Logger.info("🏦 [CLIENT_BANKER] 处理客户端申请上庄请求 - 用户: #{user_id}")
        handle_client_apply_banker(message, state, user_id)

      3907 ->  # CS_LHD_REQUEST_NOT_ZHUANG_P - 取消申请上庄
        Logger.info("🏦 [CLIENT_CANCEL_BANKER] 处理客户端取消申请上庄请求 - 用户: #{user_id}")
        handle_client_cancel_apply_banker(message, state, user_id)

      3919 ->  # CS_LHD_ZHUANG_OFF_P - 申请下庄
        Logger.info("🏦 [CLIENT_OFF_BANKER] 处理客户端申请下庄请求 - 用户: #{user_id}")
        handle_client_apply_off_banker(message, state, user_id)

      3924 ->  # CS_LHD_REQUEST_ZHUANG_LIST_P - 请求上庄列表
        Logger.info("🏦 [CLIENT_BANKER_LIST] 处理客户端请求上庄列表请求 - 用户: #{user_id}")
        handle_client_request_banker_list(message, state, user_id)

      3920 ->  # CS_LHD_ALLLIST_P - 请求玩家列表
        Logger.info("👥 [CLIENT_PLAYER_LIST] 处理客户端请求玩家列表请求 - 用户: #{user_id}")
        handle_client_request_player_list(message, state, user_id)

      3917 ->  # CS_LHD_FOLLOW_BUY_P - 续押请求
        Logger.info("🔄 [CLIENT_FOLLOW_BET] 处理客户端续押请求 - 用户: #{user_id}")
        handle_client_follow_bet(message, state, user_id)

      3915 ->  # CS_LHD_HISTORY_P - 历史记录请求
        Logger.info("📊 [CLIENT_HISTORY] 处理客户端历史记录请求 - 用户: #{user_id}")
        handle_client_get_history(message, state, user_id)

      _ ->
        Logger.warning("❓ [CLIENT_PROTOCOL] 未知的客户端协议 - SubID: #{sub_id}, 用户: #{user_id}")
        state
    end
  end



  # 私有函数

  defp handle_bet(state, user_id, area_str, amount) do
    Logger.info("🎯 [BET_VALIDATION] 开始验证下注 - 用户: #{user_id}, 区域: #{area_str}, 金额: #{amount}")
    Logger.info("🎯 [BET_VALIDATION] 当前游戏状态 - 阶段: #{state.game_data.phase}, 回合: #{state.game_data.round}")

    # 安全地转换字符串为原子，避免 badarg 错误
    area = case area_str do
      "long" -> :long
      "hu" -> :hu
      "he" -> :he
      :long -> :long
      :hu -> :hu
      :he -> :he
      _ ->
        Logger.warning("❌ [BET_ERROR] 无效的下注区域字符串 - 用户: #{user_id}, 区域: #{inspect(area_str)}")
        :invalid
    end

    cond do
      state.game_data.phase != :betting ->
        Logger.warning("❌ [BET_ERROR] 游戏阶段错误 - 用户: #{user_id}, 当前阶段: #{state.game_data.phase}, 期望阶段: betting")
        send_error(state, user_id, 14)  # GAME_ERROR_STATE_ERROR
        state

      area == :invalid or area not in [:long, :hu, :he] ->
        Logger.warning("❌ [BET_ERROR] 下注区域无效 - 用户: #{user_id}, 区域: #{area}")
        send_error(state, user_id, 15)  # GAME_ERROR_BUY_POS_ERROR
        state

      amount < state.game_data.config.min_bet ->
        Logger.warning("❌ [BET_ERROR] 下注金额过小 - 用户: #{user_id}, 金额: #{amount}, 最小: #{state.game_data.config.min_bet}")
        send_error(state, user_id, 8)   # GAME_ERROR_NOT_MONEY_TO_BET
        state

      amount > state.game_data.config.max_bet ->
        Logger.warning("❌ [BET_ERROR] 单次下注金额过大 - 用户: #{user_id}, 金额: #{amount}, 最大: #{state.game_data.config.max_bet}")
        send_error(state, user_id, 13)  # GAME_ERROR_BET_TOOMORE
        state

      true ->
        # 检查玩家积分是否足够（机器人跳过积分检查）
        if is_integer(user_id) and user_id < 0 do
          # 机器人跳过积分检查，直接进行下注验证
          validate_and_process_bet(state, user_id, area, amount)
        else
          # 真实玩家检查积分
          current_points = Cypridina.Accounts.get_user_points(user_id)
          playerid = to_playerid_key(user_id)
          user_bets = Map.get(state.game_data.bets, playerid, %{})
          user_total_bet = Enum.reduce(user_bets, 0, fn {_area, bet_amount}, acc -> acc + bet_amount end)

          if current_points < amount do
            Logger.warning("❌ [BET_ERROR] 积分不足 - 用户: #{user_id}, 当前积分: #{current_points}, 下注金额: #{amount}")
            send_error(state, user_id, 8)  # GAME_ERROR_NOT_MONEY_TO_BET
            state
          else
            # 积分足够，进行下注验证
            validate_and_process_bet(state, user_id, area, amount)
          end
        end
    end
  end

  defp start_betting_phase(state) do
    Logger.info("🐉 [LONGHU] 开始下注阶段: #{state.id}")

    # 取消之前的计时器
    if state.game_data.phase_timer do
      Process.cancel_timer(state.game_data.phase_timer)
    end

    # 启动新计时器
    timer = Process.send_after(self(), :phase_timeout, state.game_data.config.bet_time * 1000)

    game_data = %{state.game_data | phase: :betting, phase_timer: timer}

    new_state = %{state | game_data: game_data}

    # 广播下注开始
    broadcast_betting_start(new_state)

    # 让机器人开始下注
    if state.game_data.config.enable_robots do
      LongHuAI.schedule_robot_bets(new_state)
    end

    new_state
  end

  defp transition_to_dealing(state) do
    Logger.info("🐉 [LONGHU] 转换到发牌阶段: #{state.id}")

    # 发牌
    {long_card, hu_card} = LongHuLogic.deal_cards()

    game_data = %{state.game_data | phase: :dealing, cards: %{long: long_card, hu: hu_card}}

    new_state = %{state | game_data: game_data}

    # 广播发牌
    broadcast_dealing(new_state)

    # 启动发牌计时器
    timer = Process.send_after(self(), :phase_timeout, state.game_data.config.deal_time * 1000)
    game_data = %{new_state.game_data | phase_timer: timer}

    %{new_state | game_data: game_data}
  end

  defp transition_to_revealing(state) do
    Logger.info("🐉 [LONGHU] 转换到亮牌阶段: #{state.id}")

    game_data = %{state.game_data | phase: :revealing}
    new_state = %{state | game_data: game_data}

    # 广播亮牌
    broadcast_revealing(new_state)

    # 广播游戏状态 (LHD_GameState_Combine)
    broadcast_game_state(new_state)

    # 启动亮牌计时器
    timer = Process.send_after(self(), :phase_timeout, state.game_data.config.reveal_time * 1000)
    game_data = %{new_state.game_data | phase_timer: timer}

    %{new_state | game_data: game_data}
  end

  defp transition_to_settling(state) do
    Logger.info("🎯 [TRANSITION_SETTLING] 开始转换到结算阶段 - 房间: #{state.id}, 回合: #{state.game_data.round}")

    # 计算游戏结果
    result = LongHuLogic.calculate_result(state.game_data.cards.long, state.game_data.cards.hu)
    Logger.info("🎯 [TRANSITION_SETTLING] 游戏结果计算完成 - 结果: #{result}")

    # 计算玩家输赢
    settlements = calculate_settlements(state, result)
    Logger.info("🎯 [TRANSITION_SETTLING] 玩家结算计算完成 - 结算数据: #{inspect(settlements)}")

    # 更新历史记录
    history_item = %{
      round: state.game_data.round,
      cards: state.game_data.cards,
      result: result,
      total_bets: state.game_data.total_bets,
      timestamp: DateTime.utc_now()
    }

    # 保留最近50局
    new_history = [history_item | state.game_data.history] |> Enum.take(50)

    game_data = %{state.game_data | phase: :settling, history: new_history}

    new_state = %{state | game_data: game_data}

    # 广播结算结果
    Logger.info("🎯 [TRANSITION_SETTLING] 准备广播结算结果 - 协议: 3928")
    broadcast_settlement(new_state, result, settlements)
    Logger.info("🎯 [TRANSITION_SETTLING] 结算结果广播完成")

    # 结算后进行机器人管理（清理积分不足的机器人）
    final_state = if new_state.game_data.config.enable_robots do
      Logger.info("🤖 [SETTLEMENT_ROBOT_CLEANUP] 结算后清理积分不足的机器人")
      LongHuAI.cleanup_broke_robots(new_state, 500)  # 积分低于500的机器人将被清理
    else
      new_state
    end

    # 启动结算计时器
    timer = Process.send_after(self(), :phase_timeout, state.game_data.config.settle_time * 1000)
    game_data = %{final_state.game_data | phase_timer: timer}

    Logger.info("🎯 [TRANSITION_SETTLING] 结算阶段转换完成 - 结算时间: #{state.game_data.config.settle_time}秒")
    %{final_state | game_data: game_data}
  end

  defp transition_to_waiting(state) do
    Logger.info("🐉 [LONGHU] 结算完成，准备开始新一轮: #{state.id}")

    # 保存本轮下注记录用于续押
    last_round_bets = state.game_data.bets

    # 处理庄家轮换
    new_state = handle_banker_rotation(state)

    game_data = %{new_state.game_data |
      phase: :waiting,
      phase_timer: nil,
      last_round_bets: last_round_bets,
      # 清空本轮下注，准备下一轮
      bets: %{},
      total_bets: %{long: 0, hu: 0, he: 0}
    }

    final_state = %{new_state | game_data: game_data}

    # 广播等待阶段
    broadcast_waiting(final_state)

    # 百人场自动循环，2秒后开始新一轮
    Process.send_after(self(), :start_new_round, 2000)

    final_state
  end

  # 启动游戏循环
  defp start_game_loop(state) do
    Logger.info("🐉 [LONGHU] 启动百人场游戏循环: #{state.id}")

    # 添加机器人烘托气氛
    new_state = if state.game_data.config.enable_robots do
      LongHuAI.add_robots_if_needed(state)
    else
      state
    end

    # 立即开始第一轮游戏
    new_state = start_betting_phase(new_state)

    # 启动游戏tick
    schedule_game_tick()

    new_state
  end

  # 辅助函数

  defp calculate_total_bets(bets) do
    Enum.reduce(bets, %{long: 0, hu: 0, he: 0}, fn {_playerid, user_bets}, acc ->
      Enum.reduce(user_bets, acc, fn {area, amount}, area_acc ->
        Map.update(area_acc, area, amount, &(&1 + amount))
      end)
    end)
  end

  defp calculate_settlements(state, result) do
    # 计算有下注的玩家的结算，并实际处理积分变化
    state.game_data.bets
    |> Enum.filter(fn {playerid, user_bets} ->
      # 过滤掉没有下注的玩家
      playerid != nil and map_size(user_bets) > 0
    end)
    |> Enum.reduce(%{}, fn {playerid, user_bets}, acc ->
      # 将 playerid 转换回 user_id 用于积分操作
      user_id = String.to_integer(playerid)
      user_settlement =
        Enum.reduce(user_bets, 0, fn {area, amount}, total ->
          if area == result do
            # 赢了，获得赔率倍数的奖金 (包含本金)
            win_amount = amount * Map.get(state.game_data.odds, area, 1.0)
            total + win_amount
          else
            # 输了，下注金额已在下注时扣除，这里记录损失
            total - amount
          end
        end)

      # 处理真实积分结算
      if user_settlement > 0 do
        # 玩家赢钱，增加积分
        if is_integer(user_id) and user_id < 0 do
          # 机器人更新虚拟金币
          Logger.info("🤖 [SETTLEMENT] 机器人赢钱 - #{user_id}: +#{user_settlement}")
        else
          # 真实玩家增加积分
          case Accounts.add_points(user_id, user_settlement) do
            {:ok, _updated_asset} ->
              Logger.info("💰 [SETTLEMENT] 玩家赢钱积分增加成功 - #{user_id}: +#{user_settlement}")
            {:error, reason} ->
              Logger.error("❌ [SETTLEMENT] 玩家积分增加失败 - #{user_id}: #{inspect(reason)}")
          end
        end
      end

      # 记录结算数据
      if user_settlement != 0 do
        Map.put(acc, user_id, user_settlement)
      else
        acc
      end
    end)
  end

  # 时间检查函数

  defp betting_time_expired?(state) do
    case state.game_data.phase_timer do
      nil -> false
      timer_ref ->
        case Process.read_timer(timer_ref) do
          false -> true  # 计时器已过期
          _time_left -> false
        end
    end
  end

  defp dealing_time_expired?(state) do
    case state.game_data.phase_timer do
      nil -> false
      timer_ref ->
        case Process.read_timer(timer_ref) do
          false -> true
          _time_left -> false
        end
    end
  end

  defp revealing_time_expired?(state) do
    case state.game_data.phase_timer do
      nil -> false
      timer_ref ->
        case Process.read_timer(timer_ref) do
          false -> true
          _time_left -> false
        end
    end
  end

  defp settling_time_expired?(state) do
    case state.game_data.phase_timer do
      nil -> false
      timer_ref ->
        case Process.read_timer(timer_ref) do
          false -> true
          _time_left -> false
        end
    end
  end

  # 广播和发送函数

  defp send_game_config(state, user_id) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_LHD_CONFIG_P (3900)
    # 客户端期望的配置格式 (参考 onConfigs 函数)
    Logger.info("⚙️ [SEND_CONFIG] 发送游戏配置 - 用户: #{user_id}, 房间: #{state.id}")

    message = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3900,   # SC_LHD_CONFIG_P
      "data" => %{
        # 筹码配置 - 客户端期望 info.Bet 数组
        "Bet" => [100, 500, 1000, 5000, 10000],  # 筹码面值配置
        # 赔率配置 - 客户端期望 info.Odds 数组 (显示净赔率，不包含本金)
        "Odds" => %{
          "1" => 1.0,   # 龙的净赔率 (1:1)
          "2" => 1.0,   # 虎的净赔率 (1:1)
          "3" => 8.0    # 和的净赔率 (1:8)
        },
        # 下注限制
        "BetMax" => state.game_data.config.max_bet,
        "BetNeed" => state.game_data.config.min_bet,
        # 区域下注限制 - 客户端期望的 betLimit 字段
        "betLimit" => %{
          "1" => Map.get(state.game_data.config.area_bet_limits, :long, 100000),  # 龙区域限制
          "2" => Map.get(state.game_data.config.area_bet_limits, :hu, 100000),    # 虎区域限制
          "3" => Map.get(state.game_data.config.area_bet_limits, :he, 50000)      # 和区域限制
        },
        # 时间限制
        "TimeLimit" => %{
          "BuyHorse" => state.game_data.config.bet_time
        },
        # 庄家配置 (可选)
        "Zhuang" => %{
          "1" => %{  # 房间ID
            "Need" => 100000,    # 上庄所需金币
            "MinTurn" => 5,      # 最小坐庄局数
            "MaxTurn" => 20      # 最大坐庄局数
          }
        },
        # 房间底分
        "difen" => 1,
        # 当前回合ID
        "roundid" => state.game_data.round
      }
    }

    Logger.info("📤 [SEND_CONFIG] 配置消息内容 - 协议: 5/3900, 最小下注: #{state.game_data.config.min_bet}, 最大下注: #{state.game_data.config.max_bet}")
    send_to_user(state, user_id, message)
    Logger.info("✅ [SEND_CONFIG] 游戏配置已发送")
  end

  defp send_error(state, user_id, error_code) do
    # 使用客户端定义的协议格式: MainProto.XC (5) + SC_LHD_OPER_ERROR_P (3914)
    Logger.warning("❌ [SEND_ERROR] 发送错误消息 - 用户: #{user_id}, 错误码: #{error_code}")

    error_data = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3914,   # SC_LHD_OPER_ERROR_P
      "data" => %{"errorcode" => error_code}
    }

    Logger.info("📤 [SEND_ERROR] 错误消息内容 - 协议: 5/3914, 消息: #{inspect(error_data)}")
    send_to_user(state, user_id, error_data)
    Logger.info("✅ [SEND_ERROR] 错误消息已发送")
  end

  defp broadcast_betting_start(state) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_LHD_GAMESTATE_P (3901)
    # 计算剩余下注限制
    remaining_bet_limits = calculate_remaining_bet_limits(state)

    message = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3901,   # SC_LHD_GAMESTATE_P
      "data" => %{
        "state" => 1,  # LHD_GameState_BuyHorse (下注阶段)
        "round" => state.game_data.round,
        "bet_time" => state.game_data.config.bet_time,
        "total_bets" => state.game_data.total_bets,
        # 剩余下注限制 - 客户端期望的 leftbet 字段
        "leftbet" => remaining_bet_limits
      }
    }

    broadcast_to_room(state, message)
  end



  defp broadcast_dealing(state) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_LHD_FIRST_P (3902)
    # 发牌阶段对应客户端的亮牌状态 LHD_GameState_Combine (2)
    message = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3902,   # SC_LHD_FIRST_P
      "data" => %{
        "state" => 2,  # LHD_GameState_Combine (亮牌阶段)
        "round" => state.game_data.round,
        "deal_time" => state.game_data.config.deal_time
      }
    }

    broadcast_to_room(state, message)
  end

  defp broadcast_revealing(state) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_LHD_SHOWCARD_P (3912)
    # 客户端期望 msg.long 和 msg.hu，而不是 msg.cards.long
    # 这个消息只发送牌数据，不发送状态
    cards = state.game_data.cards

    # 格式化牌面数据
    long_card_data = format_card_for_client(cards.long)
    hu_card_data = format_card_for_client(cards.hu)

    Logger.info("🃏 [BROADCAST_REVEALING] 发送亮牌数据:")
    Logger.info("🃏 [BROADCAST_REVEALING] - 龙牌: #{inspect(cards.long)} -> #{inspect(long_card_data)}")
    Logger.info("🃏 [BROADCAST_REVEALING] - 虎牌: #{inspect(cards.hu)} -> #{inspect(hu_card_data)}")

    message = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3912,   # SC_LHD_SHOWCARD_P
      "data" => %{
        "round" => state.game_data.round,
        "long" => long_card_data,    # 直接放在根级别
        "hu" => hu_card_data,        # 直接放在根级别
        "reveal_time" => state.game_data.config.reveal_time
      }
    }

    Logger.info("🃏 [BROADCAST_REVEALING] 完整亮牌消息: #{inspect(message)}")
    broadcast_to_room(state, message)
  end

  defp broadcast_settlement(state, result, settlements) do
    Logger.info("📡 [BROADCAST_SETTLEMENT] 开始广播结算消息 - 协议: 3928, 回合: #{state.game_data.round}")

    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_LHD_SETTLEMENT_P_NEW (3928)
    # 客户端期望的结算消息格式 (参考 onResult 函数)
    # 根据客户端 LHDefine.ts，SC_LHD_SETTLEMENT_P_NEW 的协议ID是 3928
    cards = state.game_data.cards

    # 构建 other 字段，格式为 "玩家ID" => "剩余金币,输赢金币"
    # settlements 是一个 %{user_id => win_amount} 的映射，win_amount 是数字
    # 只包含有下注的玩家，避免发送空数据导致客户端错误
    other_data =
      settlements
      |> Enum.filter(fn {user_id, win_amount} ->
        # 只包含有效的玩家数据：用户ID不为空且输赢金额不为nil
        user_id != nil and win_amount != nil
      end)
      |> Enum.into(%{}, fn {user_id, win_amount} ->
        # 获取玩家当前真实积分
        current_money = if is_integer(user_id) and user_id < 0 do
          # 机器人使用虚拟金币
          case Map.get(state.players, user_id) do
            nil -> 0
            robot_player ->
              robot_money = Map.get(robot_player.user_info, :money, 0)
              # 更新机器人金币（结算后的金币）
              max(0, robot_money + win_amount)
          end
        else
          # 真实玩家获取当前积分
          Cypridina.Accounts.get_user_points(user_id)
        end

        # 格式: "剩余金币,输赢金币"
        {user_id, "#{current_money},#{win_amount}"}
      end)

    Logger.info("🐉 [SETTLEMENT] 结算数据: #{inspect(other_data)}")

    # 构建庄家数据 - 客户端期望的庄家信息
    zhuang_data = %{
      "playerid" => to_numeric_id(-1),  # 系统庄家ID，使用-1表示系统庄家
      "playercoin" => 1000000, # 庄家剩余金币（系统庄家设置为较大值）
      "nChange" => 0           # 庄家输赢金额（暂时设为0，后续可以根据实际逻辑计算）
    }

    # 生成唯一的消息ID用于追踪
    message_id = "settlement_#{state.game_data.round}_#{System.system_time(:millisecond)}"

    message = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3928,   # SC_LHD_SETTLEMENT_P_NEW (客户端绑定的是3928，不是3913)
      "data" => %{
        "roundid" => state.game_data.round,              # 客户端期望 roundid
        "resultPos" => result_to_direction(result),      # 客户端期望 resultPos (1=龙, 2=虎, 3=和)
        "win_value" => calculate_win_value(state, result),      # 获胜区域的总下注金额
        "other" => other_data,                           # 玩家结算数据，格式: "playerid" => "剩余金币,输赢金币"
        "_playerid" => nil,                              # 当前玩家ID (可选)
        "nextat" => nil,                                 # 下一轮时间 (可选)
        "zhuang" => zhuang_data,                         # 庄家数据 - 客户端期望的格式
        "message_id" => message_id                       # 唯一消息ID，用于追踪重复
      }
    }

    Logger.info("📡 [BROADCAST_SETTLEMENT] 结算消息ID: #{message_id}")
    Logger.info("📡 [BROADCAST_SETTLEMENT] 结算消息内容: #{inspect(message)}")
    Logger.info("📡 [BROADCAST_SETTLEMENT] 开始广播给房间内所有玩家 - 玩家数量: #{map_size(state.players)}")

    broadcast_to_room(state, message)

    Logger.info("📡 [BROADCAST_SETTLEMENT] 结算消息广播完成 - 协议: 5/3928, 消息ID: #{message_id}")
  end

  defp broadcast_waiting(state) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_LHD_GAMESTATE_P (3901)
    message = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3901,   # SC_LHD_GAMESTATE_P
      "data" => %{
        "state" => 3,  # LHD_GameState_End (游戏休息,等下一局开始)
        "round" => state.game_data.round,
        "next_round_delay" => 2000
      }
    }

    broadcast_to_room(state, message)
  end

  # 广播玩家数量变化通知 (使用现有的3929协议)
  defp broadcast_player_count_change(state) do
    # 使用现有的协议格式: MainProto.XC (5) + SC_LHD_PLAYER_COUNT_P (3929)
    # 通知前端玩家数量变化
    message = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3929,   # SC_LHD_PLAYER_COUNT_P (玩家数量变化通知)
      "data" => %{
        "totalplayernum" => map_size(state.players)  # 当前房间总人数
      }
    }

    Logger.info("📤 [BROADCAST_PLAYER_COUNT] 广播玩家数量变化 - 房间总人数: #{map_size(state.players)}")
    broadcast_to_room(state, message)
  end


  # 获取玩家当前金币（机器人使用虚拟金币，真实玩家使用积分）
  defp get_player_current_money(state, user_id) do
    if is_integer(user_id) and user_id < 0 do
      # 机器人使用虚拟金币
      case Map.get(state.players, user_id) do
        nil -> 0
        robot_player -> Map.get(robot_player.user_info, :money, 0)
      end
    else
      # 真实玩家使用积分
      Cypridina.Accounts.get_user_points(user_id)
    end
  end

  # 更新玩家信息
  defp update_player_info(state, user_id, updated_user_info) do
    case Map.get(state.players, user_id) do
      nil -> state
      player ->
        updated_player = %{player | user_info: updated_user_info}
        updated_players = Map.put(state.players, user_id, updated_player)
        %{state | players: updated_players}
    end
  end

  # 验证并处理下注
  defp validate_and_process_bet(state, user_id, area, amount) do
    playerid = to_playerid_key(user_id)
    user_bets = Map.get(state.game_data.bets, playerid, %{})
    current_bet = Map.get(user_bets, area, 0)
    new_bet = current_bet + amount

    # 计算用户总下注
    user_total_bet = Enum.reduce(user_bets, 0, fn {_area, bet_amount}, acc -> acc + bet_amount end) + amount

    # 计算该区域总下注
    area_total_bet = Map.get(state.game_data.total_bets, area, 0) + amount

    cond do
      # 检查个人总下注限制
      user_total_bet > state.game_data.config.max_total_bet ->
        Logger.warning("❌ [BET_ERROR] 个人总下注超限 - 用户: #{user_id}, 总下注: #{user_total_bet}, 限制: #{state.game_data.config.max_total_bet}")
        send_error(state, user_id, 1)  # GAME_ERROR_BUY_LIMIT
        state

      # 检查区域下注限制
      area_total_bet > Map.get(state.game_data.config.area_bet_limits, area, 999999) ->
        Logger.warning("❌ [BET_ERROR] 区域下注超限 - 用户: #{user_id}, 区域: #{area}, 区域总下注: #{area_total_bet}, 限制: #{Map.get(state.game_data.config.area_bet_limits, area)}")
        send_error(state, user_id, 1)  # GAME_ERROR_BUY_LIMIT
        state

      true ->
        Logger.info("✅ [BET_VALIDATION] 下注验证通过 - 用户: #{user_id}")

        # 扣除真实玩家的积分（机器人跳过）
        if not (is_integer(user_id) and user_id < 0) do
          case Accounts.subtract_points(user_id, amount) do
            {:ok, _updated_asset} ->
              Logger.info("💰 [POINTS_DEDUCT] 积分扣除成功 - 用户: #{user_id}, 金额: #{amount}")
              process_successful_bet(state, user_id, area, amount, user_bets, current_bet, new_bet)
            {:error, reason} ->
              Logger.error("❌ [POINTS_DEDUCT] 积分扣除失败 - 用户: #{user_id}, 原因: #{inspect(reason)}")
              send_error(state, user_id, 8)  # GAME_ERROR_NOT_MONEY_TO_BET
              state
          end
        else
          # 机器人直接处理下注，更新虚拟金币
          process_successful_bet(state, user_id, area, amount, user_bets, current_bet, new_bet)
        end
    end
  end

  # 处理成功的下注
  defp process_successful_bet(state, user_id, area, amount, user_bets, current_bet, new_bet) do
    # 更新玩家下注
    playerid = to_playerid_key(user_id)
    new_user_bets = Map.put(user_bets, area, new_bet)
    new_bets = Map.put(state.game_data.bets, playerid, new_user_bets)

    # 更新总下注
    old_total_bets = state.game_data.total_bets
    new_total_bets = calculate_total_bets(new_bets)

    Logger.info("📊 [BET_TOTALS] 总下注更新 - 旧: #{inspect(old_total_bets)}, 新: #{inspect(new_total_bets)}")

    # 更新机器人的虚拟金币
    updated_state = if is_integer(user_id) and user_id < 0 do
      LongHuAI.update_robot_money(state, user_id, -amount)
    else
      # 更新真实玩家的显示金币为当前积分
      current_points = Cypridina.Accounts.get_user_points(user_id)
      update_player_display_money(state, user_id, current_points)
    end

    game_data = %{updated_state.game_data | bets: new_bets, total_bets: new_total_bets}
    new_state = %{updated_state | game_data: game_data}

    Logger.info("📢 [BET_BROADCAST] 准备广播下注成功 - 用户: #{user_id}")

    # 发送下注成功确认给玩家 (SC_LHD_BET_SUCCESS 3926) - 发送给下注玩家
    send_bet_success_confirmation(new_state, user_id, area, amount)

    # 广播下注同步 (SC_LHD_BET_SYNC 3927) - 广播给所有玩家
    broadcast_bet_sync(new_state, user_id, area, amount)

    Logger.info("🎉 [BET_SUCCESS] 下注处理完成 - 用户: #{user_id}, 区域: #{area}, 金额: #{amount}, 总计: #{new_bet}")

    new_state
  end

  # 更新玩家显示金币
  defp update_player_display_money(state, user_id, new_money) do
    case Map.get(state.players, user_id) do
      nil -> state
      player ->
        updated_user_info = Map.put(player.user_info, :money, new_money)
        updated_player = %{player | user_info: updated_user_info}
        updated_players = Map.put(state.players, user_id, updated_player)
        %{state | players: updated_players}
    end
  end

  # 发送游戏状态给指定用户 (SC_LHD_GAMESTATE_P 3901)
  defp send_game_state_to_user(state, user_id) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_LHD_GAMESTATE_P (3901)
    game_state_data = format_game_state_for_client(state)

    message = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3901,   # SC_LHD_GAMESTATE_P
      "data" => game_state_data
    }

    Logger.info("📤 [SEND_GAME_STATE] 发送游戏状态给用户 - 用户: #{user_id}, 状态: #{state.game_data.phase}")
    send_to_user(state, user_id, message)
  end

  # 广播游戏状态给所有玩家 (SC_LHD_GAMESTATE_P 3901)
  defp broadcast_game_state(state) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_LHD_GAMESTATE_P (3901)
    game_state_data = format_game_state_for_client(state)

    message = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3901,   # SC_LHD_GAMESTATE_P
      "data" => game_state_data
    }

    Logger.info("📤 [BROADCAST_GAME_STATE] 广播游戏状态 - 状态: #{state.game_data.phase}")
    broadcast_to_room(state, message)
  end

  # 格式化游戏状态为客户端期望的格式
  defp format_game_state_for_client(state) do
    # 计算剩余时间
    remaining_time = calculate_remaining_time(state)

    # 计算剩余下注限制
    remaining_bet_limits = calculate_remaining_bet_limits(state)

    # 根据当前游戏阶段返回对应的状态数据
    case state.game_data.phase do
      :betting ->
        %{
          "state" => 1,  # LHD_GameState_BuyHorse (下注阶段)
          "round" => state.game_data.round,
          "bet_time" => state.game_data.config.bet_time,
          "remaining_time" => remaining_time,
          "total_bets" => state.game_data.total_bets,
          "leftbet" => remaining_bet_limits
        }

      :dealing ->
        %{
          "state" => 2,  # LHD_GameState_Combine (亮牌阶段)
          "round" => state.game_data.round,
          "deal_time" => state.game_data.config.deal_time,
          "remaining_time" => remaining_time
        }

      :revealing ->
        %{
          "state" => 2,  # LHD_GameState_Combine (亮牌阶段)
          "round" => state.game_data.round,
          "reveal_time" => state.game_data.config.reveal_time,
          "remaining_time" => remaining_time
        }

      :settling ->
        %{
          "state" => 3,  # LHD_GameState_End (结算阶段)
          "round" => state.game_data.round,
          "settle_time" => state.game_data.config.settle_time,
          "remaining_time" => remaining_time
        }

      :waiting ->
        %{
          "state" => 3,  # LHD_GameState_End (游戏休息,等下一局开始)
          "round" => state.game_data.round,
          "next_round_delay" => 2000,
          "remaining_time" => remaining_time
        }

      _ ->
        %{
          "state" => 0,  # LHD_GameState_None (无状态)
          "round" => state.game_data.round,
          "remaining_time" => 0
        }
    end
  end

  # 辅助函数：获取玩家在各区域的下注总额
  defp get_player_bets(state, user_id) do
    playerid = to_playerid_key(user_id)
    player_bets = Map.get(state.game_data.bets, playerid, %{})

    %{
      long: Map.get(player_bets, :long, 0),
      hu: Map.get(player_bets, :hu, 0),
      he: Map.get(player_bets, :he, 0)
    }
  end

  # 辅助函数：计算剩余下注限制 (1=龙, 2=虎, 3=和)
  defp calculate_remaining_bet_limits(state) do
    # 获取原始区域限制
    area_limits = state.game_data.config.area_bet_limits
    # 获取当前各区域总下注
    total_bets = state.game_data.total_bets

    # 计算剩余可下注额度 = 原始限制 - 当前总下注
    %{
      "1" => max(0, Map.get(area_limits, :long, 100000) - Map.get(total_bets, :long, 0)),  # 龙区域剩余
      "2" => max(0, Map.get(area_limits, :hu, 100000) - Map.get(total_bets, :hu, 0)),      # 虎区域剩余
      "3" => max(0, Map.get(area_limits, :he, 50000) - Map.get(total_bets, :he, 0))        # 和区域剩余
    }
  end

  # 辅助函数：将区域转换为客户端期望的direction (1=龙, 2=虎, 3=和)
  defp area_to_direction(area) do
    case area do
      "long" -> 1
      "hu" -> 2
      "he" -> 3
      :long -> 1
      :hu -> 2
      :he -> 3
      _ -> 1  # 默认为龙
    end
  end

  # 辅助函数：安全地获取玩家在指定区域的下注
  defp get_player_bet_for_area(player_bets, area) do
    area_atom = case area do
      "long" -> :long
      "hu" -> :hu
      "he" -> :he
      :long -> :long
      :hu -> :hu
      :he -> :he
      _ -> :long
    end

    Map.get(player_bets, area_atom, 0)
  end

  # 辅助函数：格式化所有玩家下注信息
  defp format_all_players_bets(state) do
    state.game_data.bets
    |> Enum.map(fn {playerid, user_bets} ->
      # 将 playerid 转换回 user_id 用于客户端显示
      user_id = String.to_integer(playerid)

      # 安全地获取各区域下注，使用 Map.get 避免 KeyError
      long_bet = Map.get(user_bets, :long, 0) || Map.get(user_bets, "long", 0)
      hu_bet = Map.get(user_bets, :hu, 0) || Map.get(user_bets, "hu", 0)
      he_bet = Map.get(user_bets, :he, 0) || Map.get(user_bets, "he", 0)

      %{
        "playerid" => to_numeric_id(user_id),
        "bets" => %{
          "long" => long_bet,
          "hu" => hu_bet,
          "he" => he_bet
        },
        "total" => long_bet + hu_bet + he_bet
      }
    end)
  end

  # 辅助函数：格式化玩家同步数据
  defp format_players_sync_data(state) do
    state.game_data.bets
    |> Enum.map(fn {playerid, user_bets} ->
      # 将 playerid 转换回 user_id 用于客户端显示
      user_id = String.to_integer(playerid)

      # 安全地获取各区域下注，使用 Map.get 避免 KeyError
      long_bet = Map.get(user_bets, :long, 0) || Map.get(user_bets, "long", 0)
      hu_bet = Map.get(user_bets, :hu, 0) || Map.get(user_bets, "hu", 0)
      he_bet = Map.get(user_bets, :he, 0) || Map.get(user_bets, "he", 0)

      %{
        "playerid" => to_numeric_id(user_id),
        "sync_data" => %{
          "long" => long_bet,
          "hu" => hu_bet,
          "he" => he_bet,
          "last_bet_time" => DateTime.utc_now() |> DateTime.to_unix(:millisecond)
        }
      }
    end)
  end

  # 辅助函数：计算剩余时间
  defp calculate_remaining_time(state) do
    case state.game_data.phase_timer do
      nil ->
        0
      timer_ref ->
        case Process.read_timer(timer_ref) do
          false -> 0  # 计时器已过期
          time_left -> div(time_left, 1000)  # 转换为秒
        end
    end
  end

  # 广播筹码增量信息 (SC_LHD_BET_SYNC 3927)
  defp broadcast_bet_sync(state, user_id, area, amount) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_LHD_BET_SYNC (3927)
    # 客户端期望的筹码增量信息，使用位运算编码

    # 获取玩家在各区域的下注总额
    player_bets = get_player_bets(state, user_id)

    # 计算更新后的区域下注限制 (剩余可下注额度)
    remaining_bet_limits = calculate_remaining_bet_limits(state)

    # 根据客户端 onBetSYNC 函数分析，需要编码筹码信息
    # 客户端使用位运算解码：tabArr: [0, 4, 8, 12, 16, 20]
    # 每个筹码面值对应4位，最多支持6种筹码面值
    encoded_bets = encode_bet_for_sync(amount, area)

    Logger.info("🎰 [BROADCAST_BET_SYNC] 构建筹码增量广播 - 用户: #{user_id}, 区域: #{area}, 金额: #{amount}")
    Logger.info("🎰 [BROADCAST_BET_SYNC] 编码后的下注数据: #{inspect(encoded_bets)}")

    message = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3927,   # SC_LHD_BET_SYNC
      "data" => %{
        "roundid" => state.game_data.round,
        "total" => state.game_data.total_bets,  # 当前总下注
        "leftbet" => remaining_bet_limits,      # 剩余下注限制
        # 玩家筹码增量信息 - 使用 playerid 作为键
        to_playerid_key(user_id) => encoded_bets
      }
    }

    Logger.info("📤 [BROADCAST_BET_SYNC] 发送筹码增量广播 - 协议: 5/3927, 消息: #{inspect(message)}")
    Logger.info("🚫 [BROADCAST_BET_SYNC] 排除下注玩家: #{user_id}")
    broadcast_to_room(state, message, user_id)
    Logger.info("✅ [BROADCAST_BET_SYNC] 筹码增量广播已发送 (已排除下注玩家)")

    state
  end

  # 辅助函数：编码下注信息为客户端期望的位运算格式
  defp encode_bet_for_sync(amount, area) do
    # 客户端筹码面值配置 (通常是: [100, 500, 1000, 5000, 10000, 50000])
    # 根据下注金额确定使用哪种筹码面值
    chip_values = [100, 500, 1000, 5000, 10000, 50000]

    # 计算每种筹码的数量
    {encoded_long, encoded_hu, encoded_he} = case area do
      :long -> {encode_chips(amount, chip_values), 0, 0}
      "long" -> {encode_chips(amount, chip_values), 0, 0}
      :hu -> {0, encode_chips(amount, chip_values), 0}
      "hu" -> {0, encode_chips(amount, chip_values), 0}
      :he -> {0, 0, encode_chips(amount, chip_values)}
      "he" -> {0, 0, encode_chips(amount, chip_values)}
      _ -> {encode_chips(amount, chip_values), 0, 0}
    end

    %{
      "long" => encoded_long,
      "hu" => encoded_hu,
      "he" => encoded_he,
      "dirctionall" => case area do
        :long -> amount
        "long" -> amount
        :hu -> amount
        "hu" -> amount
        :he -> amount
        "he" -> amount
        _ -> amount
      end
    }
  end

  # 辅助函数：将金额编码为筹码位运算格式
  defp encode_chips(amount, chip_values) do
    # 贪心算法：从大到小使用筹码
    {encoded, _remaining} = Enum.reduce(chip_values, {0, amount}, fn chip_value, {acc_encoded, remaining} ->
      if remaining >= chip_value do
        chip_count = min(div(remaining, chip_value), 15)  # 每种筹码最多15个 (4位)
        used_amount = chip_count * chip_value
        new_remaining = remaining - used_amount

        # 根据筹码面值确定位移位置
        shift_bits = case chip_value do
          100 -> 0      # 第1种筹码，位移0位
          500 -> 4      # 第2种筹码，位移4位
          1000 -> 8     # 第3种筹码，位移8位
          5000 -> 12    # 第4种筹码，位移12位
          10000 -> 16   # 第5种筹码，位移16位
          50000 -> 20   # 第6种筹码，位移20位
          _ -> 0
        end

        new_encoded = Bitwise.bor(acc_encoded, Bitwise.bsl(chip_count, shift_bits))
        {new_encoded, new_remaining}
      else
        {acc_encoded, remaining}
      end
    end)

    encoded
  end



  # 广播下注同步 (SC_LHD_BET_SYNC 3927) - 用于同步所有玩家下注状态
  defp broadcast_sync_bet(state, user_id, area, amount) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_LHD_BET_SYNC (3927)
    # 根据客户端 onPlayerBet 函数分析，这个协议用于同步所有玩家的下注状态

    # 获取当前房间所有玩家的下注信息，格式化为客户端期望的格式
    all_players_sync_data = format_players_sync_data(state)

    # 获取各区域总下注
    area_totals = %{
      "long" => state.game_data.total_bets.long || 0,
      "hu" => state.game_data.total_bets.hu || 0,
      "he" => state.game_data.total_bets.he || 0
    }

    Logger.info("🔄 [BROADCAST_SYNC_BET] 构建下注同步广播 - 用户: #{user_id}, 区域: #{area}, 金额: #{amount}")
    Logger.info("🔄 [BROADCAST_SYNC_BET] 区域总下注 - #{inspect(area_totals)}")

    message = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3927,   # SC_LHD_BET_SYNC
      "data" => %{
        "roundid" => state.game_data.round,
        "trigger_player" => user_id,              # 触发同步的玩家ID
        "trigger_area" => area_to_direction(area), # 触发同步的下注区域
        "trigger_amount" => amount,               # 触发同步的下注金额
        # 各区域总下注 - 客户端期望的格式
        "area_totals" => area_totals,
        # 所有玩家下注同步数据
        "players_data" => all_players_sync_data,
        # 当前游戏状态信息
        "game_state" => %{
          "phase" => state.game_data.phase,
          "round" => state.game_data.round,
          "remaining_time" => calculate_remaining_time(state)
        },
        "timestamp" => DateTime.utc_now() |> DateTime.to_unix(:millisecond)
      }
    }

    Logger.info("📤 [BROADCAST_SYNC_BET] 发送下注同步广播 - 协议: 5/3927, 消息: #{inspect(message)}")
    broadcast_to_room(state, message)
    Logger.info("✅ [BROADCAST_SYNC_BET] 下注同步广播已发送")

    state
  end

  # 发送下注成功确认 (SC_LHD_BET_SUCCESS 3926) - 发送给下注玩家
  defp send_bet_success_confirmation(state, user_id, area, amount) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_LHD_BET_SUCCESS (3926)
    # 根据客户端 onBetSuccess 函数分析，这个协议用于确认下注成功

    # 获取玩家在各区域的下注总额
    player_bets = get_player_bets(state, user_id)
    direction = area_to_direction(area)

    # 计算更新后的区域下注限制 (剩余可下注额度)
    remaining_bet_limits = calculate_remaining_bet_limits(state)

    Logger.info("✅ [SEND_BET_SUCCESS] 构建下注成功确认 - 用户: #{user_id}, 区域: #{area}(#{direction}), 金额: #{amount}")
    Logger.info("✅ [SEND_BET_SUCCESS] 玩家下注详情 - 龙: #{player_bets.long}, 虎: #{player_bets.hu}, 和: #{player_bets.he}")

    message = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3926,   # SC_LHD_BET_SUCCESS
      "data" => %{
        "code" => 1,                              # 成功状态码 (1=成功)
        "odds" => amount,                         # 下注金额 - 客户端期望的字段名
        "direction" => direction,                 # 下注方向 (1=龙, 2=虎, 3=和)
        "long" => player_bets.long,               # 玩家在龙区域的总下注
        "hu" => player_bets.hu,                   # 玩家在虎区域的总下注
        "he" => player_bets.he,                   # 玩家在和区域的总下注
        "long_total" => state.game_data.total_bets.long, # 龙区域总下注
        "hu_total" => state.game_data.total_bets.hu,     # 虎区域总下注
        "he_total" => state.game_data.total_bets.he,     # 和区域总下注
        "chouma" => get_player_current_money(state, user_id),  # 玩家当前金币
        "limit" => remaining_bet_limits,          # 下注限制
        "totalbet" => state.game_data.total_bets  # 总下注信息
      }
    }

    Logger.info("📤 [SEND_BET_SUCCESS] 发送下注成功确认 - 协议: 5/3926, 消息: #{inspect(message)}")
    send_to_user(state, user_id, message)
    Logger.info("✅ [SEND_BET_SUCCESS] 下注成功确认已发送")

    state
  end

  # ==================== 庄家系统处理函数 ====================

  # 处理申请上庄
  defp handle_apply_banker(state, user_id) do
    Logger.info("🏦 [APPLY_BANKER] 处理申请上庄 - 用户: #{user_id}")

    # 检查用户是否已在队列中
    already_in_queue = Enum.any?(state.game_data.banker_queue, fn banker -> banker.user_id == user_id end)

    cond do
      already_in_queue ->
        Logger.warning("❌ [APPLY_BANKER] 用户已在上庄队列中 - 用户: #{user_id}")
        send_banker_error(state, user_id, 7)  # GAME_ERROR_APPLYZHUANG_OK (已申请)
        state

      # TODO: 检查用户金币是否足够 (需要获取用户信息)
      # user_money < banker_need ->

      true ->
        # 添加到上庄队列
        new_banker = %{
          user_id: user_id,
          name: "玩家#{user_id}",  # TODO: 获取真实用户名
          money: 100000,          # TODO: 获取真实用户金币
          apply_time: System.system_time(:millisecond)
        }

        new_queue = state.game_data.banker_queue ++ [new_banker]
        game_data = %{state.game_data | banker_queue: new_queue}
        new_state = %{state | game_data: game_data}

        Logger.info("✅ [APPLY_BANKER] 申请上庄成功 - 用户: #{user_id}, 队列长度: #{length(new_queue)}")

        # 广播上庄列表更新
        broadcast_banker_list(new_state)

        # 发送申请成功消息
        send_banker_success(new_state, user_id, "申请上庄成功")

        new_state
    end
  end

  # 处理取消申请上庄
  defp handle_cancel_apply_banker(state, user_id) do
    Logger.info("🏦 [CANCEL_APPLY_BANKER] 处理取消申请上庄 - 用户: #{user_id}")

    # 从队列中移除用户
    new_queue = Enum.reject(state.game_data.banker_queue, fn banker -> banker.user_id == user_id end)

    if length(new_queue) == length(state.game_data.banker_queue) do
      Logger.warning("❌ [CANCEL_APPLY_BANKER] 用户不在上庄队列中 - 用户: #{user_id}")
      send_banker_error(state, user_id, 1)  # 通用错误
      state
    else
      game_data = %{state.game_data | banker_queue: new_queue}
      new_state = %{state | game_data: game_data}

      Logger.info("✅ [CANCEL_APPLY_BANKER] 取消申请上庄成功 - 用户: #{user_id}")

      # 广播上庄列表更新
      broadcast_banker_list(new_state)

      # 发送取消成功消息
      send_banker_success(new_state, user_id, "取消申请上庄成功")

      new_state
    end
  end

  # 处理申请下庄
  defp handle_apply_off_banker(state, user_id) do
    Logger.info("🏦 [APPLY_OFF_BANKER] 处理申请下庄 - 用户: #{user_id}")

    current_banker = state.game_data.banker

    cond do
      current_banker.user_id != user_id ->
        Logger.warning("❌ [APPLY_OFF_BANKER] 用户不是当前庄家 - 用户: #{user_id}")
        send_banker_error(state, user_id, 3)  # GAME_ERROR_OZ_STATE
        state

      current_banker.turns_played < current_banker.min_turns ->
        Logger.warning("❌ [APPLY_OFF_BANKER] 坐庄局数不足 - 用户: #{user_id}, 已坐: #{current_banker.turns_played}, 最少: #{current_banker.min_turns}")
        send_banker_error(state, user_id, 2)  # GAME_ERROR_NOT_ROUND
        # 发送还需坐庄局数提示
        remaining_rounds = current_banker.min_turns - current_banker.turns_played
        send_banker_notice(state, user_id, remaining_rounds)
        state

      true ->
        # 添加到下庄申请列表
        if user_id not in state.game_data.banker_off_requests do
          new_off_requests = [user_id | state.game_data.banker_off_requests]
          game_data = %{state.game_data | banker_off_requests: new_off_requests}
          new_state = %{state | game_data: game_data}

          Logger.info("✅ [APPLY_OFF_BANKER] 申请下庄成功 - 用户: #{user_id}")

          # 发送申请下庄成功消息
          send_banker_success(new_state, user_id, "申请下庄成功，将在下一轮下庄")

          new_state
        else
          Logger.info("ℹ️ [APPLY_OFF_BANKER] 用户已申请下庄 - 用户: #{user_id}")
          send_banker_success(state, user_id, "已申请下庄")
          state
        end
    end
  end

  # 处理请求上庄列表
  defp handle_request_banker_list(state, user_id) do
    Logger.info("🏦 [REQUEST_BANKER_LIST] 处理请求上庄列表 - 用户: #{user_id}")

    # 直接发送当前上庄列表给请求用户
    send_banker_list_to_user(state, user_id)

    state
  end

  # 处理请求玩家列表
  defp handle_request_player_list(state, user_id, page) do
    Logger.info("👥 [REQUEST_PLAYER_LIST] 处理请求玩家列表 - 用户: #{user_id}, 页码: #{page}")

    # 发送玩家列表给请求用户
    send_player_list_to_user(state, user_id, page)

    state
  end

  # 处理续押请求
  defp handle_follow_bet(state, user_id) do
    Logger.info("🔄 [FOLLOW_BET] 处理续押请求 - 用户: #{user_id}")

    # 检查是否在下注阶段
    if state.game_data.phase != :betting do
      Logger.warning("❌ [FOLLOW_BET] 不在下注阶段 - 用户: #{user_id}")
      send_follow_bet_error(state, user_id, "当前不在下注阶段")
      state
    else

    # 获取上一轮的下注记录
    playerid = to_playerid_key(user_id)
    last_bets = Map.get(state.game_data.last_round_bets, playerid, %{})

    if map_size(last_bets) == 0 do
      Logger.warning("❌ [FOLLOW_BET] 上一轮无下注记录 - 用户: #{user_id}")
      send_follow_bet_error(state, user_id, "上一轮无下注记录")
      state
    else
      Logger.info("🔄 [FOLLOW_BET] 执行续押 - 用户: #{user_id}, 下注: #{inspect(last_bets)}")

      # 逐个执行上一轮的下注
      final_state = Enum.reduce(last_bets, state, fn {area, amount}, acc_state ->
        # 使用内部下注处理函数
        handle_bet(acc_state, user_id, Atom.to_string(area), amount)
      end)

      # 发送续押成功消息
      send_follow_bet_success(final_state, user_id, "续押成功")

      final_state
    end
    end
  end

  # 处理请求奖池信息
  defp handle_request_jackpot(state, user_id) do
    Logger.info("🎰 [REQUEST_JACKPOT] 处理请求奖池信息 - 用户: #{user_id}")

    # 发送奖池信息给请求用户
    send_jackpot_info_to_user(state, user_id)

    state
  end

  # 处理请求时间配置
  defp handle_request_time_config(state, user_id) do
    Logger.info("⏰ [REQUEST_TIME_CONFIG] 处理请求时间配置 - 用户: #{user_id}")

    # 发送时间配置给请求用户
    send_time_config_to_user(state, user_id)

    state
  end

  # 辅助函数：格式化单张牌数据为客户端期望的格式
  defp format_card_for_client(card) do
    %{
      "color" => suit_to_color(card.suit),  # 客户端期望的是color字段
      "number" => card.rank,                # 客户端期望的是number字段
      "suit" => card.suit,
      "rank" => card.rank,
      "value" => card.value
    }
  end

  # 辅助函数：将花色转换为客户端期望的颜色值
  # 根据客户端资源文件 card.plist 的实际花色编码
  defp suit_to_color(suit) do
    case suit do
      :hearts -> 3    # 红桃 (poker_3xx.png)
      :diamonds -> 4  # 方块 (poker_4xx.png)
      :clubs -> 5     # 梅花 (poker_5xx.png)
      :spades -> 6    # 黑桃 (poker_6xx.png)
      _ -> 3          # 默认红桃
    end
  end

  # 辅助函数：将结果转换为客户端期望的方向值 (1=龙, 2=虎, 3=和)
  defp result_to_direction(result) do
    case result do
      :long -> 1   # 龙
      :hu -> 2     # 虎
      :tie -> 3    # 和
      :he -> 3     # 和 (别名)
      _ -> 1       # 默认为龙
    end
  end

  # 计算获胜区域的总下注金额
  defp calculate_win_value(state, result) do
    case result do
      :long -> state.game_data.total_bets.long  # 龙区域的总下注
      :hu -> state.game_data.total_bets.hu      # 虎区域的总下注
      :tie -> state.game_data.total_bets.he     # 和区域的总下注
      _ -> 0
    end
  end

  # 游戏循环和机器人逻辑

  defp schedule_game_tick() do
    # 每秒检查一次游戏状态
    Process.send_after(self(), :game_tick, 1000)
  end

  # ==================== 庄家广播函数 ====================

  # 广播上庄列表更新
  defp broadcast_banker_list(state) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_LHD_ZHUANG_LIST_P (3908)
    banker_list_data = format_banker_list_for_client(state)

    message = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3908,   # SC_LHD_ZHUANG_LIST_P
      "data" => banker_list_data
    }

    Logger.info("📤 [BROADCAST_BANKER_LIST] 广播上庄列表更新 - 队列长度: #{length(state.game_data.banker_queue)}")
    broadcast_to_room(state, message)
  end

  # 发送上庄列表给指定用户
  defp send_banker_list_to_user(state, user_id) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_LHD_ZHUANG_LIST_P (3908)
    banker_list_data = format_banker_list_for_client(state)

    message = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3908,   # SC_LHD_ZHUANG_LIST_P
      "data" => banker_list_data
    }

    Logger.info("📤 [SEND_BANKER_LIST] 发送上庄列表给用户 - 用户: #{user_id}")
    send_to_user(state, user_id, message)
  end

  # 广播庄家信息更新
  defp broadcast_banker_info(state) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_LHD_ZHUANG_INFO_P (3909)
    banker_info_data = format_banker_info_for_client(state)

    message = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3909,   # SC_LHD_ZHUANG_INFO_P
      "data" => banker_info_data
    }

    Logger.info("📤 [BROADCAST_BANKER_INFO] 广播庄家信息更新 - 庄家: #{state.game_data.banker.user_id}")
    broadcast_to_room(state, message)
  end

  # 发送庄家信息给指定用户
  defp send_banker_info_to_user(state, user_id) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_LHD_ZHUANG_INFO_P (3909)
    banker_info_data = format_banker_info_for_client(state)

    message = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3909,   # SC_LHD_ZHUANG_INFO_P
      "data" => banker_info_data
    }

    Logger.info("📤 [SEND_BANKER_INFO] 发送庄家信息给用户 - 用户: #{user_id}")
    send_to_user(state, user_id, message)
  end

  # 发送庄家操作成功消息
  defp send_banker_success(state, user_id, message) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_LHD_OPER_ERROR_P (3914)
    # 但是用 code = 1 表示成功
    success_data = %{
      "code" => 1,
      "msg" => message
    }

    response = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3914,   # SC_LHD_OPER_ERROR_P
      "data" => success_data
    }

    Logger.info("📤 [SEND_BANKER_SUCCESS] 发送庄家操作成功 - 用户: #{user_id}, 消息: #{message}")
    send_to_user(state, user_id, response)
  end

  # 发送庄家操作错误消息
  defp send_banker_error(state, user_id, error_code) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_LHD_OPER_ERROR_P (3914)
    error_data = %{
      "code" => error_code
    }

    response = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3914,   # SC_LHD_OPER_ERROR_P
      "data" => error_data
    }

    Logger.info("📤 [SEND_BANKER_ERROR] 发送庄家操作错误 - 用户: #{user_id}, 错误码: #{error_code}")
    send_to_user(state, user_id, response)
  end

  # 发送庄家下庄通知
  defp send_banker_notice(state, user_id, remaining_rounds) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_LHD_NOTICE_NO_ZHUANG_P (3911)
    notice_data = %{
      "round" => remaining_rounds
    }

    response = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3911,   # SC_LHD_NOTICE_NO_ZHUANG_P
      "data" => notice_data
    }

    Logger.info("📤 [SEND_BANKER_NOTICE] 发送庄家下庄通知 - 用户: #{user_id}, 剩余局数: #{remaining_rounds}")
    send_to_user(state, user_id, response)
  end

  # 处理机器人管理消息
  def handle_info(:manage_robots, state) do
    if state.game_data.config.enable_robots do
      Logger.info("🤖 [MANAGE_ROBOTS] 开始动态管理机器人 - 房间: #{state.id}")

      # 执行动态机器人管理
      new_state = LongHuAI.manage_robots_dynamically(state)

      # 广播玩家数量变化
      broadcast_player_count_change(new_state)

      # 安排下次检查（30秒后）
      Process.send_after(self(), :manage_robots, 30_000)

      {:noreply, new_state}
    else
      {:noreply, state}
    end
  end

  # 处理机器人下注消息
  def handle_info({:robot_bet, robot_id}, state) do
    if state.game_data.phase == :betting do
      # 获取机器人信息
      robot_player = Map.get(state.players, robot_id)

      if robot_player && robot_player.is_robot do
        # 检查机器人积分是否足够
        robot_money = Map.get(robot_player.user_info, :money, 0)

        if robot_money < 1000 do
          # 积分不足，移除机器人
          Logger.info("🤖 [ROBOT_BET] 机器人积分不足，移除: #{robot_id} (积分: #{robot_money})")
          new_state = LongHuAI.remove_robot_from_room(state, robot_id)
          {:noreply, new_state}
        else
          # 生成智能机器人下注决策
          {area, amount} = LongHuAI.generate_smart_robot_bet(robot_player, state)

          # 执行机器人下注
          new_state = handle_bet(state, robot_id, Atom.to_string(area), amount)

          Logger.info("🤖 [ROBOT_BET] 机器人下注: #{robot_id} -> #{area}: #{amount} (风格: #{Map.get(robot_player.user_info, :bet_style, :moderate)}, 剩余积分: #{robot_money})")

          {:noreply, new_state}
        end
      else
        Logger.warning("🤖 [ROBOT_BET] 机器人不存在或无效: #{robot_id}")
        {:noreply, state}
      end
    else
      Logger.debug("🤖 [ROBOT_BET] 游戏阶段不是下注阶段: #{state.game_data.phase}")
      {:noreply, state}
    end
  end

  # 处理阶段超时
  def handle_info(:phase_timeout, state) do
    Logger.info("🐉 [PHASE_TIMEOUT] 阶段超时触发 - 当前阶段: #{state.game_data.phase}, 回合: #{state.game_data.round}")

    new_state = case state.game_data.phase do
      :betting ->
        Logger.info("🐉 [PHASE_TIMEOUT] 下注阶段超时，转换到发牌阶段")
        transition_to_dealing(state)
      :dealing ->
        Logger.info("🐉 [PHASE_TIMEOUT] 发牌阶段超时，转换到亮牌阶段")
        transition_to_revealing(state)
      :revealing ->
        Logger.info("🐉 [PHASE_TIMEOUT] 亮牌阶段超时，转换到结算阶段")
        transition_to_settling(state)
      :settling ->
        Logger.info("🐉 [PHASE_TIMEOUT] 结算阶段超时，转换到等待阶段")
        transition_to_waiting(state)
      _ ->
        Logger.warning("🐉 [PHASE_TIMEOUT] 未知阶段超时: #{state.game_data.phase}")
        state
    end

    {:noreply, new_state}
  end

  # 处理开始新一轮消息
  def handle_info(:start_new_round, state) do
    Logger.info("🐉 [LONGHU] 开始新一轮游戏")

    new_state = on_game_start(state)
    {:noreply, new_state}
  end

  @impl true
  def handle_game_tick(state) do
    # 继续调度下一次tick
    if state.game_data.phase != :waiting do
      schedule_game_tick()
    end
    state
  end

  # ==================== 数据格式化函数 ====================

  # 格式化上庄列表为客户端期望的格式
  defp format_banker_list_for_client(state) do
    # 客户端期望的格式参考 onUpdateBankerList 函数
    banker_list = state.game_data.banker_queue
    |> Enum.with_index(1)
    |> Enum.into(%{}, fn {banker, index} ->
      {index, %{
        "playerid" => to_numeric_id(banker.user_id),
        "playername" => banker.name,
        "playercoin" => banker.money,
        "sort" => index
      }}
    end)

    %{
      "list" => banker_list,
      "isempty" => length(state.game_data.banker_queue) == 0
    }
  end

  # 格式化庄家信息为客户端期望的格式
  defp format_banker_info_for_client(state) do
    banker = state.game_data.banker
    remaining_bet_limits = calculate_remaining_bet_limits(state)

    # 客户端期望的格式参考 onBankerInfo 函数
    %{
      "zhuangheadid" => banker.user_id || -1,    # 庄家头像ID (系统庄家用-1)
      "zhuangid" => banker.user_id || -1,        # 庄家ID (系统庄家用-1)
      "zhuangturn" => banker.turns_played,       # 已坐庄局数
      "chouma" => banker.money,                  # 庄家金币
      "zhuangname" => banker.name,               # 庄家名称
      "issystem" => if(banker.is_system, do: 1, else: 0),  # 是否系统庄家
      # 剩余下注限制
      "leftbet" => remaining_bet_limits,
      # 上庄列表 (可选)
      "list" => format_banker_list_for_client(state)
    }
  end

  # ==================== 庄家轮换逻辑 ====================

  # 处理庄家轮换
  defp handle_banker_rotation(state) do
    current_banker = state.game_data.banker

    Logger.info("🏦 [BANKER_ROTATION] 检查庄家轮换 - 当前庄家: #{current_banker.user_id}, 已坐庄: #{current_banker.turns_played}")

    # 增加庄家坐庄局数
    updated_banker = %{current_banker | turns_played: current_banker.turns_played + 1}

    # 检查是否需要下庄
    should_rotate = should_banker_rotate?(updated_banker, state.game_data.banker_off_requests, state.game_data.banker_queue)

    if should_rotate do
      Logger.info("🏦 [BANKER_ROTATION] 庄家需要下庄 - 原因: #{get_rotation_reason(updated_banker, state.game_data.banker_off_requests)}")
      rotate_banker(state, updated_banker)
    else
      # 更新庄家局数
      game_data = %{state.game_data | banker: updated_banker}
      new_state = %{state | game_data: game_data}

      # 广播庄家信息更新
      broadcast_banker_info(new_state)

      new_state
    end
  end

  # 判断庄家是否应该下庄
  defp should_banker_rotate?(banker, off_requests, banker_queue \\ []) do
    cond do
      # 系统庄家：有玩家申请上庄时让位
      banker.is_system and length(banker_queue) > 0 ->
        true

      # 系统庄家：没有玩家申请时继续坐庄
      banker.is_system ->
        false

      # 达到最大坐庄局数
      banker.turns_played >= banker.max_turns ->
        true

      # 庄家申请下庄且已达到最小局数
      banker.user_id in off_requests and banker.turns_played >= banker.min_turns ->
        true

      # 庄家金币不足 (TODO: 实现金币检查)
      # banker.money < min_banker_money ->
      #   true

      true ->
        false
    end
  end

  # 获取下庄原因
  defp get_rotation_reason(banker, off_requests) do
    cond do
      banker.turns_played >= banker.max_turns ->
        "达到最大坐庄局数(#{banker.max_turns})"

      banker.user_id in off_requests ->
        "庄家申请下庄"

      true ->
        "其他原因"
    end
  end

  # 执行庄家轮换
  defp rotate_banker(state, current_banker) do
    Logger.info("🏦 [ROTATE_BANKER] 执行庄家轮换")

    # 清除当前庄家的下庄申请
    new_off_requests = List.delete(state.game_data.banker_off_requests, current_banker.user_id)

    # 选择下一个庄家
    {new_banker, remaining_queue} = select_next_banker(state.game_data.banker_queue)

    # 更新游戏数据
    game_data = %{state.game_data |
      banker: new_banker,
      banker_queue: remaining_queue,
      banker_off_requests: new_off_requests
    }

    new_state = %{state | game_data: game_data}

    Logger.info("🏦 [ROTATE_BANKER] 庄家轮换完成 - 新庄家: #{new_banker.user_id}")

    # 广播庄家变更
    broadcast_banker_change(new_state, current_banker, new_banker)

    new_state
  end

  # 选择下一个庄家
  defp select_next_banker(banker_queue) do
    case banker_queue do
      [] ->
        # 没有排队的玩家，继续使用系统庄家
        Logger.info("🏦 [SELECT_BANKER] 无排队玩家，继续使用系统庄家")
        system_banker = %{
          user_id: nil,
          name: "系统庄家",
          money: 1000000,
          turns_played: 0,
          max_turns: 999,
          min_turns: 0,
          is_system: true
        }
        {system_banker, []}

      [next_banker | remaining_queue] ->
        # 使用队列中的下一个玩家
        Logger.info("🏦 [SELECT_BANKER] 选择队列中的下一个庄家: #{next_banker.user_id}")
        player_banker = %{
          user_id: next_banker.user_id,
          name: next_banker.name,
          money: next_banker.money,
          turns_played: 0,
          max_turns: 20,
          min_turns: 5,
          is_system: false
        }
        {player_banker, remaining_queue}
    end
  end

  # 广播庄家变更
  defp broadcast_banker_change(state, old_banker, new_banker) do
    # 发送下庄通知 (如果是玩家庄家)
    if not old_banker.is_system do
      send_banker_off_notice(state, old_banker.user_id)
    end

    # 广播新庄家信息
    broadcast_banker_info(state)

    # 广播上庄列表更新
    broadcast_banker_list(state)
  end

  # 发送下庄通知
  defp send_banker_off_notice(state, user_id) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_LHD_ZHUANG_OFF_P (3910)
    message = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3910,   # SC_LHD_ZHUANG_OFF_P
      "data" => %{
        "code" => 1,
        "msg" => "下庄成功"
      }
    }

    Logger.info("📤 [SEND_BANKER_OFF] 发送下庄通知 - 用户: #{user_id}")
    send_to_user(state, user_id, message)
  end

  # 发送玩家列表给指定用户
  defp send_player_list_to_user(state, user_id, page) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_LHD_ALLLIST_P (3921)
    player_list_data = format_player_list_for_client(state, page)

    message = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3921,   # SC_LHD_ALLLIST_P
      "data" => player_list_data
    }

    Logger.info("📤 [SEND_PLAYER_LIST] 发送玩家列表给用户 - 用户: #{user_id}, 页码: #{page}")
    send_to_user(state, user_id, message)
  end

  # 格式化玩家列表为客户端期望的格式
  defp format_player_list_for_client(state, page) do
    # 每页显示的玩家数量
    page_size = 50
    start_index = page * page_size

    # 获取所有玩家（包括机器人，按下注金额排序）
    all_players = state.players
    |> Enum.to_list()
    |> Enum.map(fn {user_id, player} ->
      # 计算玩家本轮总下注
      playerid = to_playerid_key(user_id)
      user_bets = Map.get(state.game_data.bets, playerid, %{})
      total_bet = Enum.reduce(user_bets, 0, fn {_area, amount}, acc -> acc + amount end)

      {user_id, player, total_bet}
    end)
    |> Enum.sort_by(fn {_user_id, _player, total_bet} -> -total_bet end)  # 按下注金额降序排列

    total_count = length(all_players)

    # 如果是第0页，返回排行榜数据和玩家列表
    if page == 0 do
      # 取前50名作为排行榜
      betrank_players = all_players
      |> Enum.take(50)
      |> Enum.with_index(1)
      |> Enum.into(%{}, fn {{user_id, player, total_bet}, index} ->
        {index, %{
          "playerid" => to_numeric_id(user_id),
          "nickname" => Map.get(player.user_info, :nickname, "玩家#{user_id}"),
          "name" => Map.get(player.user_info, :nickname, "玩家#{user_id}"),
          "coin" => Map.get(player.user_info, :money, 10000),  # 玩家金币
          "playercoin" => Map.get(player.user_info, :money, 10000),
          "bet" => total_bet,  # 本轮下注金额
          "headid" => Map.get(player.user_info, :avatar_id, 1),  # 头像ID
          "wxheadurl" => Map.get(player.user_info, :avatar_url, ""),  # 头像URL
          "winnum" => 0,  # 胜利次数 (TODO: 实现统计)
          "sort" => index
        }}
      end)

      # 客户端期望的格式 (参考 initPlayerListData 函数)
      %{
        "totalplayernum" => total_count,  # 总在线玩家数
        "betrank" => betrank_players      # 下注排行榜数据
      }
    else
      # 分页获取玩家列表
      page_players = all_players
      |> Enum.drop(start_index)
      |> Enum.take(page_size)
      |> Enum.with_index(start_index + 1)
      |> Enum.into(%{}, fn {{user_id, player, total_bet}, index} ->
        {index, %{
          "playerid" => to_numeric_id(user_id),
          "nickname" => Map.get(player.user_info, :nickname, "玩家#{user_id}"),
          "name" => Map.get(player.user_info, :nickname, "玩家#{user_id}"),
          "coin" => Map.get(player.user_info, :money, 10000),
          "playercoin" => Map.get(player.user_info, :money, 10000),
          "bet" => total_bet,
          "headid" => Map.get(player.user_info, :avatar_id, 1),
          "wxheadurl" => Map.get(player.user_info, :avatar_url, ""),
          "winnum" => 0,
          "sort" => index
        }}
      end)

      # 分页数据格式
      %{
        "playerlist" => page_players,
        "page" => page,
        "totalplayernum" => total_count
      }
    end
  end

  # ==================== 续押、奖池、时间配置发送函数 ====================

  # 发送续押成功消息
  defp send_follow_bet_success(state, user_id, message) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_FOLLOW_BUY_P
    success_data = %{
      "code" => 1,
      "msg" => message
    }

    response = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3927,   # SC_FOLLOW_BUY_P (续押成功)
      "data" => success_data
    }

    Logger.info("📤 [SEND_FOLLOW_BET_SUCCESS] 发送续押成功 - 用户: #{user_id}, 消息: #{message}")
    send_to_user(state, user_id, response)
  end

  # 发送续押错误消息
  defp send_follow_bet_error(state, user_id, message) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_FOLLOW_BUY_P
    error_data = %{
      "code" => 1,
      "msg" => message
    }

    response = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3927,   # SC_FOLLOW_BUY_P (续押失败)
      "data" => error_data
    }

    Logger.info("📤 [SEND_FOLLOW_BET_ERROR] 发送续押错误 - 用户: #{user_id}, 消息: #{message}")
    send_to_user(state, user_id, response)
  end

  # 发送奖池信息给指定用户
  defp send_jackpot_info_to_user(state, user_id) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_COLOR_POOL_P
    jackpot_data = format_jackpot_for_client(state)

    message = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3929,   # SC_COLOR_POOL_P
      "data" => jackpot_data
    }

    Logger.info("📤 [SEND_JACKPOT_INFO] 发送奖池信息给用户 - 用户: #{user_id}")
    send_to_user(state, user_id, message)
  end

  # 发送时间配置给指定用户
  defp send_time_config_to_user(state, user_id) do
    # 使用客户端期望的协议格式: MainProto.XC (5) + SC_TIME_P
    time_config_data = format_time_config_for_client(state)

    message = %{
      "mainId" => 5,     # MainProto.XC
      "subId" => 3930,   # SC_TIME_P
      "data" => time_config_data
    }

    Logger.info("📤 [SEND_TIME_CONFIG] 发送时间配置给用户 - 用户: #{user_id}")
    send_to_user(state, user_id, message)
  end

  # 格式化奖池信息为客户端期望的格式
  defp format_jackpot_for_client(state) do
    jackpot = state.game_data.jackpot

    # 客户端期望的奖池格式
    %{
      "total" => jackpot.total,
      "long" => jackpot.long,
      "hu" => jackpot.hu,
      "he" => jackpot.he,
      # 奖池增长率 (可选)
      "growth_rate" => 0.01,
      # 当前回合
      "round" => state.game_data.round
    }
  end

  # 格式化时间配置为客户端期望的格式
  defp format_time_config_for_client(state) do
    config = state.game_data.config

    # 计算当前阶段剩余时间
    remaining_time = case state.game_data.phase_timer do
      nil -> 0
      timer_ref ->
        case Process.read_timer(timer_ref) do
          false -> 0
          time_left -> div(time_left, 1000)  # 转换为秒
        end
    end

    # 客户端期望的时间配置格式
    %{
      "current_phase" => phase_to_client_state(state.game_data.phase),
      "remaining_time" => remaining_time,
      "bet_time" => config.bet_time,
      "deal_time" => config.deal_time,
      "reveal_time" => config.reveal_time,
      "settle_time" => config.settle_time,
      "round" => state.game_data.round
    }
  end

  # 将游戏阶段转换为客户端状态
  defp phase_to_client_state(phase) do
    case phase do
      :waiting -> 0    # 等待阶段
      :betting -> 1    # 下注阶段
      :dealing -> 2    # 发牌阶段
      :revealing -> 2  # 亮牌阶段 (客户端合并为发牌阶段)
      :settling -> 3   # 结算阶段
      _ -> 0
    end
  end

  # ==================== 协议处理功能 ====================
  # 直接处理客户端协议，无需协议号转换

  # 客户端协议ID映射 (对应IndiaGameClient中的LHDefine.ts)
  @client_protocol_ids %{
    # 对应客户端 SC_LHD_CONFIG_P (3900) - 发送配置
    sc_lhd_config: 3900,
    # 对应客户端 SC_LHD_GAMESTATE_P (3901) - 游戏状态切换
    sc_lhd_gamestate: 3901,
    # 对应客户端 SC_LHD_FIRST_P (3902) - 开始发牌动画
    sc_lhd_first: 3902,
    # 对应客户端 SC_LHD_OPTTIME_P (3903) - 下注亮牌时间
    sc_lhd_opttime: 3903,
    # 对应客户端 CS_LHD_BUYHORSE_P (3904) - 请求下注
    cs_lhd_buyhorse: 3904,
    # 对应客户端 SC_LHD_BUYHORSE_P (3905) - 下注响应
    sc_lhd_buyhorse: 3905,
    # 对应客户端 CS_LHD_REQUEST_ZHUANG_P (3906) - 请求上庄
    cs_lhd_request_zhuang: 3906,
    # 对应客户端 CS_LHD_REQUEST_NOT_ZHUANG_P (3907) - 请求取消上庄
    cs_lhd_request_not_zhuang: 3907,
    # 对应客户端 SC_LHD_ZHUANG_LIST_P (3908) - 上庄列表
    sc_lhd_zhuang_list: 3908,
    # 对应客户端 SC_LHD_ZHUANG_INFO_P (3909) - 庄家信息
    sc_lhd_zhuang_info: 3909,
    # 对应客户端 SC_LHD_NO_ZHUANG_P (3910) - 下庄公告
    sc_lhd_no_zhuang: 3910,
    # 对应客户端 SC_LHD_NOTICE_NO_ZHUANG_P (3911) - 通知庄家可以开始主动下庄
    sc_lhd_notice_no_zhuang: 3911,
    # 对应客户端 SC_LHD_SHOWCARD_P (3912) - 通知亮牌操作
    sc_lhd_showcard: 3912,
    # 对应客户端 SC_LHD_SETTLEMENT_P (3913) - 比牌结果&结算
    sc_lhd_settlement: 3913,
    # 对应客户端 SC_LHD_OPER_ERROR_P (3914) - 服务端返回操作错误码
    sc_lhd_oper_error: 3914,
    # 对应客户端 CS_LHD_HISTORY_P (3915) - 请求历史信息
    cs_lhd_history: 3915,
    # 对应客户端 SC_LHD_HISTORY_P (3916) - 返回历史信息
    sc_lhd_history: 3916,
    # 对应客户端 CS_LHD_FOLLOW_BUY_P (3917) - 请求续投
    cs_lhd_follow_buy: 3917,
    # 对应客户端 SC_LHD_FOLLOW_BUY_P (3918) - 续投
    sc_lhd_follow_buy: 3918,
    # 对应客户端 CS_LHD_ZHUANG_OFF_P (3919) - 当前庄请求下庄
    cs_lhd_zhuang_off: 3919,
    # 对应客户端 CS_LHD_ALLLIST_P (3920) - 请求玩家列表
    cs_lhd_alllist: 3920,
    # 对应客户端 SC_LHD_ALLLIST_P (3921) - 返回玩家列表
    sc_lhd_alllist: 3921,
    # 对应客户端 SC_LHD_BETINFO (3922) - 下注信息
    sc_lhd_betinfo: 3922,
    # 对应客户端 SC_LHD_SYNC_BET (3923) - 下注同步
    sc_lhd_sync_bet: 3923,
    # 对应客户端 CS_LHD_REQUEST_ZHUANG_LIST_P (3924) - 请求上庄列表
    cs_lhd_request_zhuang_list: 3924,
    # 对应客户端 SC_LHD_BET_SUCCESS (3926) - 下注成功
    sc_lhd_bet_success: 3926,
    # 对应客户端 SC_LHD_BET_SYNC (3927) - 筹码增量信息
    sc_lhd_bet_sync: 3927,
    # 对应客户端 SC_LHD_SETTLEMENT_P_NEW (3928) - 新结算协议
    sc_lhd_settlement_new: 3928,
    # 对应客户端 SC_LHD_ONLINENUM_P (3929) - 桌面上在线玩家数
    sc_lhd_onlinenum: 3929
  }

  @doc """
  处理客户端下注请求 - 直接处理，无需协议转换
  """
  def handle_client_bet(%{"mainId" => 5, "subId" => 3904} = message, state, user_id) do
    Logger.info("🐉 [CLIENT_BET] 处理客户端下注请求: #{inspect(message["data"])}")

    # 解析客户端数据格式
    data = message["data"] || %{}
    direction = Map.get(data, "direction", 1)  # 客户端发送的方向 (1=龙, 2=虎, 3=和)
    odds = Map.get(data, "odds", 0)            # 客户端发送的下注金额

    # 转换方向为内部格式 (客户端数据: 1=龙, 2=虎, 3=和)
    area = case direction do
      1 -> :long  # 龙
      2 -> :hu    # 虎
      3 -> :he    # 和
      _ -> :long  # 默认为龙
    end

    amount = odds

    # 验证下注参数
    cond do
      amount <= 0 ->
        send_client_error(state, user_id, 8)   # GAME_ERROR_NOT_MONEY_TO_BET
        state

      true ->
        # 直接调用内部下注处理
        handle_bet(state, user_id, area, amount)
    end
  end

  @doc """
  处理客户端申请上庄请求
  """
  def handle_client_apply_banker(%{"mainId" => 5, "subId" => 3906} = _message, state, user_id) do
    Logger.info("🐉 [CLIENT_BANKER] 处理客户端申请上庄请求 - 用户: #{user_id}")
    handle_apply_banker(state, user_id)
  end

  @doc """
  处理客户端取消申请上庄请求
  """
  def handle_client_cancel_apply_banker(%{"mainId" => 5, "subId" => 3907} = _message, state, user_id) do
    Logger.info("🐉 [CLIENT_CANCEL_BANKER] 处理客户端取消申请上庄请求 - 用户: #{user_id}")
    handle_cancel_apply_banker(state, user_id)
  end

  @doc """
  处理客户端申请下庄请求
  """
  def handle_client_apply_off_banker(%{"mainId" => 5, "subId" => 3919} = _message, state, user_id) do
    Logger.info("🐉 [CLIENT_OFF_BANKER] 处理客户端申请下庄请求 - 用户: #{user_id}")
    handle_apply_off_banker(state, user_id)
  end

  @doc """
  处理客户端请求上庄列表请求
  """
  def handle_client_request_banker_list(%{"mainId" => 5, "subId" => 3924} = _message, state, user_id) do
    Logger.info("🐉 [CLIENT_BANKER_LIST] 处理客户端请求上庄列表请求 - 用户: #{user_id}")
    handle_request_banker_list(state, user_id)
  end

  @doc """
  处理客户端请求玩家列表请求
  """
  def handle_client_request_player_list(%{"mainId" => 5, "subId" => 3920} = message, state, user_id) do
    Logger.info("🐉 [CLIENT_PLAYER_LIST] 处理客户端请求玩家列表请求 - 用户: #{user_id}")

    data = message["data"] || %{}
    page = Map.get(data, "page", 0)

    handle_request_player_list(state, user_id, page)
  end

  @doc """
  处理客户端续押请求
  """
  def handle_client_follow_bet(%{"mainId" => 5, "subId" => 3917} = _message, state, user_id) do
    Logger.info("🐉 [CLIENT_FOLLOW_BET] 处理客户端续押请求 - 用户: #{user_id}")
    handle_follow_bet(state, user_id)
  end

  @doc """
  处理客户端历史记录请求
  """
  def handle_client_get_history(%{"mainId" => 5, "subId" => 3915} = message, state, user_id) do
    Logger.info("🐉 [CLIENT_HISTORY] 处理客户端历史记录请求 - 用户: #{user_id}")

    data = message["data"] || %{}
    page = Map.get(data, "page", 0)
    count = Map.get(data, "count", 20)

    # 发送历史记录给客户端
    send_history_to_user(state, user_id, page, count)
    state
  end

  # 发送历史记录给客户端
  defp send_history_to_user(state, user_id, page, count) do
    Logger.info("📊 [SEND_HISTORY] 发送历史记录 - 用户: #{user_id}, 页码: #{page}, 数量: #{count}")

    # 获取历史记录
    history = state.game_data.history
    total_count = length(history)

    # 分页处理
    start_index = page * count
    end_index = min(start_index + count, total_count)

    page_history = if start_index < total_count do
      history |> Enum.slice(start_index, count)
    else
      []
    end

    # 格式化历史记录为客户端期望的格式
    formatted_history = Enum.map(page_history, fn item ->
      %{
        "round" => item.round,
        "result" => format_result_for_client(item.result),
        "cards" => %{
          "long" => format_card_for_client(item.cards.long),
          "hu" => format_card_for_client(item.cards.hu)
        },
        "total_bets" => item.total_bets,
        "timestamp" => DateTime.to_unix(item.timestamp, :millisecond)
      }
    end)

    # 发送历史记录响应 (SC_LHD_HISTORY_P 3916)
    history_data = %{
      "page" => page,
      "count" => length(formatted_history),
      "total" => total_count,
      "history" => formatted_history
    }

    message = format_client_message(:sc_lhd_history, history_data)
    send_to_user(state, user_id, message)

    Logger.info("✅ [SEND_HISTORY] 历史记录已发送 - 用户: #{user_id}, 发送条数: #{length(formatted_history)}")
  end

  # 格式化游戏结果为客户端格式
  defp format_result_for_client(result) do
    case result do
      :long -> 1  # 龙赢
      :hu -> 2    # 虎赢
      :he -> 3    # 和
      _ -> 0      # 未知
    end
  end

  # 发送客户端错误消息
  defp send_client_error(state, user_id, error_code) do
    error_data = %{
      "errorcode" => error_code
    }

    message = format_client_message(:sc_lhd_oper_error, error_data)
    send_to_user(state, user_id, message)
  end

  @doc """
  格式化客户端协议消息 (兼容IndiaGameClient)
  """
  def format_client_message(protocol_key, data) do
    protocol_id = Map.get(@client_protocol_ids, protocol_key)

    if protocol_id do
      %{
        "mainId" => 5,  # XC主协议
        "subId" => protocol_id,
        "data" => data,
        "timestamp" => DateTime.utc_now() |> DateTime.to_unix(:millisecond)
      }
    else
      Logger.error("🐉 [PROTOCOL_ERROR] 未知的客户端协议: #{protocol_key}")
      nil
    end
  end

  @doc """
  获取客户端协议ID映射
  """
  def get_client_protocol_ids(), do: @client_protocol_ids
end
