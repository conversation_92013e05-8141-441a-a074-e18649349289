defmodule Cypridina.Teen.GameSystem.Games.LongHu.LongHuAI do
  @moduledoc """
  龙虎斗游戏AI机器人模块

  功能：
  - 管理机器人的创建和生命周期
  - 实现智能下注策略
  - 处理机器人行为和个性系统
  - 机器人金币管理和状态维护
  """

  require Logger

  # 机器人名称池
  @robot_names [
    "龙虎大师", "幸运玩家", "高手在民间", "财神爷", "金币猎人",
    "运气王", "策略大师", "赌神传人", "机器战士", "AI玩家",
    "算法高手", "数据分析师", "概率专家", "统计学家", "智能机器人",
    "龙腾虎跃", "风云变幻", "一掷千金", "稳赢不亏", "神算子"
  ]

  @doc """
  为房间添加所需的机器人

  ## 参数
  - state: 房间状态
  """
  def add_robots_if_needed(state) do
    current_robot_count = count_robots(state)
    target_robot_count = state.game_data.config.robot_count

    if current_robot_count < target_robot_count do
      needed_robots = target_robot_count - current_robot_count
      Logger.info("🤖 [ADD_ROBOTS] 当前机器人: #{current_robot_count}, 目标: #{target_robot_count}, 需要添加: #{needed_robots}")

      Enum.reduce(1..needed_robots, state, fn i, acc_state ->
        add_single_robot(acc_state, i)
      end)
    else
      state
    end
  end

  @doc """
  动态管理机器人 - 清退低积分机器人并补充新机器人

  ## 参数
  - state: 房间状态
  """
  def manage_robots_dynamically(state) do
    # 1. 清退积分不足的机器人
    state_after_cleanup = cleanup_broke_robots(state)

    # 2. 补充新机器人到目标数量
    state_after_refill = add_robots_if_needed(state_after_cleanup)

    # 3. 随机轮换部分老机器人（保持新鲜感）
    rotate_old_robots(state_after_refill)
  end

  @doc """
  添加单个机器人

  ## 参数
  - state: 房间状态
  - index: 机器人序号
  """
  def add_single_robot(state, index \\ 1) do
    # 使用负数作为机器人ID，基于房间ID、时间戳和序号生成唯一负数
    room_hash = :erlang.phash2(state.id, 1000)
    time_hash = :erlang.phash2(DateTime.utc_now(), 1000)
    robot_id = -(1000000 + room_hash * 1000 + time_hash + index)

    # 随机选择机器人名称
    robot_name = Enum.random(@robot_names)

    robot_info = %{
      nickname: robot_name,
      avatar: "robot_#{rem(index, 5) + 1}",  # 使用robot_前缀的头像
      is_robot: true,
      money: generate_robot_initial_money(),  # 使用新的金币生成逻辑
      level: :rand.uniform(10),  # 随机等级 1-10
      # 机器人个性参数
      aggression: :rand.uniform(),  # 激进程度 0-1
      bet_frequency: 0.8 + :rand.uniform() * 0.2,  # 下注频率 80%-100%
      favorite_area: Enum.random([:long, :hu, :he]),  # 偏好区域
      bet_style: Enum.random([:conservative, :moderate, :aggressive]),  # 下注风格
      created_at: DateTime.utc_now()  # 创建时间
    }

    robot_data = %{
      user_id: robot_id,
      user_info: robot_info,
      joined_at: DateTime.utc_now(),
      is_ready: true,
      is_robot: true
    }

    Logger.info("🤖 [ADD_ROBOT] 添加新机器人: #{robot_id} (#{robot_name}) - 积分: #{robot_info.money}")

    %{state | players: Map.put(state.players, robot_id, robot_data)}
  end

  @doc """
  为机器人安排下注时间

  ## 参数
  - state: 房间状态
  """
  def schedule_robot_bets(state) do
    # 让机器人在整个下注阶段都保持活跃
    robot_players = Enum.filter(state.players, fn {_id, player} -> player.is_robot end)
    bet_time_ms = state.game_data.config.bet_time * 1000  # 转换为毫秒

    Logger.info("🤖 [SCHEDULE_ROBOT_BETS] 调度 #{length(robot_players)} 个机器人在 #{state.game_data.config.bet_time}秒内积极下注")

    Enum.each(robot_players, fn {robot_id, player} ->
      # 每个机器人都会下注，时间分散到整个下注阶段
      aggression = Map.get(player.user_info, :aggression, 0.5)
      bet_style = Map.get(player.user_info, :bet_style, :moderate)

      # 第一次下注 - 所有机器人都参与，分散在前1/3时间段
      first_delay = 1000 + :rand.uniform(trunc(bet_time_ms / 3))  # 前5秒
      Process.send_after(self(), {:robot_bet, robot_id}, first_delay)
      Logger.info("🤖 [SCHEDULE_ROBOT_BETS] 机器人 #{robot_id} 第1次下注: #{first_delay}ms")

      # 第二次下注 - 70%的机器人，分散在中间1/3时间段
      if aggression > 0.3 do
        second_delay = trunc(bet_time_ms / 3) + :rand.uniform(trunc(bet_time_ms / 3))  # 5-10秒
        Process.send_after(self(), {:robot_bet, robot_id}, second_delay)
        Logger.info("🤖 [SCHEDULE_ROBOT_BETS] 机器人 #{robot_id} 第2次下注: #{second_delay}ms")
      end

      # 第三次下注 - 激进型机器人，分散在后1/3时间段
      if aggression > 0.7 and bet_style == :aggressive do
        third_delay = trunc(bet_time_ms * 2 / 3) + :rand.uniform(trunc(bet_time_ms / 3))  # 10-15秒
        Process.send_after(self(), {:robot_bet, robot_id}, third_delay)
        Logger.info("🤖 [SCHEDULE_ROBOT_BETS] 机器人 #{robot_id} 第3次下注: #{third_delay}ms")
      end

      # 第四次下注 - 超级激进机器人，在最后阶段
      if aggression > 0.9 and :rand.uniform() < 0.4 do  # 提高概率到40%
        fourth_delay = trunc(bet_time_ms * 0.8) + :rand.uniform(trunc(bet_time_ms * 0.2))  # 12-15秒
        Process.send_after(self(), {:robot_bet, robot_id}, fourth_delay)
        Logger.info("🤖 [SCHEDULE_ROBOT_BETS] 机器人 #{robot_id} 第4次下注: #{fourth_delay}ms")
      end

      # 额外的随机下注 - 让一些机器人在随机时间再次下注
      if :rand.uniform() < 0.3 do  # 30%概率
        random_delay = 2000 + :rand.uniform(bet_time_ms - 3000)  # 2秒到倒数1秒
        Process.send_after(self(), {:robot_bet, robot_id}, random_delay)
        Logger.info("🤖 [SCHEDULE_ROBOT_BETS] 机器人 #{robot_id} 随机下注: #{random_delay}ms")
      end
    end)
  end

  @doc """
  生成智能机器人下注决策

  ## 参数
  - robot_player: 机器人玩家数据
  - state: 游戏状态

  ## 返回
  {area, amount} - 下注区域和金额
  """
  def generate_smart_robot_bet(robot_player, state) do
    robot_info = robot_player.user_info
    history = state.game_data.history
    total_bets = state.game_data.total_bets
    config = state.game_data.config

    # 机器人个性参数
    aggression = Map.get(robot_info, :aggression, 0.5)
    _favorite_area = Map.get(robot_info, :favorite_area, :long)
    bet_style = Map.get(robot_info, :bet_style, :moderate)

    # 选择下注区域的策略
    area = choose_robot_bet_area(robot_info, history, total_bets)

    # 使用筹码配置中的面额进行下注
    chip_values = [100, 500, 1000, 5000, 10000]  # 筹码面值配置
    amount = calculate_robot_bet_amount(bet_style, aggression, chip_values, config)

    {area, amount}
  end

  @doc """
  选择机器人下注区域

  ## 参数
  - robot_info: 机器人信息
  - history: 历史记录
  - total_bets: 当前总下注

  ## 返回
  下注区域原子
  """
  def choose_robot_bet_area(robot_info, history, total_bets) do
    favorite_area = Map.get(robot_info, :favorite_area, :long)
    _aggression = Map.get(robot_info, :aggression, 0.5)

    # 策略权重
    strategy_roll = :rand.uniform(100)

    cond do
      # 30% 概率选择偏好区域
      strategy_roll <= 30 ->
        favorite_area

      # 40% 概率跟随热门区域
      strategy_roll <= 70 ->
        get_popular_area(total_bets)

      # 20% 概率基于历史趋势
      strategy_roll <= 90 ->
        get_trending_area(history)

      # 10% 概率完全随机
      true ->
        Enum.random([:long, :hu, :he])
    end
  end

  @doc """
  计算机器人下注金额 - 更像真人的下注方式

  ## 参数
  - bet_style: 下注风格 (:conservative, :moderate, :aggressive)
  - aggression: 激进程度 (0-1)
  - chip_values: 筹码面值列表
  - config: 游戏配置

  ## 返回
  下注金额
  """
  def calculate_robot_bet_amount(bet_style, aggression, chip_values, config) do
    # 筹码面值权重配置 - 便宜筹码权重更高
    chip_weights = %{
      100 => 40,    # 100筹码：40%权重
      500 => 25,    # 500筹码：25%权重
      1000 => 20,   # 1000筹码：20%权重
      5000 => 10,   # 5000筹码：10%权重
      10000 => 5    # 10000筹码：5%权重
    }

    # 根据下注风格选择筹码数量范围（一次只下一种筹码）
    {min_quantity, max_quantity} = case bet_style do
      :conservative -> {1, 3}    # 保守型：1-3个同种筹码
      :moderate -> {2, 5}        # 中庸型：2-5个同种筹码
      :aggressive -> {3, 8}      # 激进型：3-8个同种筹码
    end

    # 根据激进程度调整数量上限
    adjusted_max_quantity = trunc(max_quantity * (0.6 + aggression * 0.4))
    chip_quantity = min_quantity + :rand.uniform(max(1, adjusted_max_quantity - min_quantity))

    # 按权重随机选择一种筹码面值
    selected_chip_value = select_chip_by_weight(chip_values, chip_weights)

    # 计算总下注金额（只使用一种筹码）
    total_amount = selected_chip_value * chip_quantity

    # 确保在游戏限制范围内
    final_amount = max(config.min_bet, min(total_amount, config.max_bet))

    # 如果超出限制，调整为最大允许的该筹码倍数
    if final_amount < total_amount do
      max_quantity_allowed = div(config.max_bet, selected_chip_value)
      selected_chip_value * max(1, max_quantity_allowed)
    else
      final_amount
    end
  end

  @doc """
  按权重选择筹码面值

  ## 参数
  - chip_values: 筹码面值列表
  - chip_weights: 筹码权重映射

  ## 返回
  选中的筹码面值
  """
  defp select_chip_by_weight(chip_values, chip_weights) do
    # 计算总权重
    total_weight = chip_values
    |> Enum.map(fn value -> Map.get(chip_weights, value, 1) end)
    |> Enum.sum()

    # 生成随机数
    random_value = :rand.uniform(total_weight)

    # 按权重选择筹码
    {selected_chip, _} = chip_values
    |> Enum.reduce_while({nil, 0}, fn chip_value, {_selected, accumulated_weight} ->
      weight = Map.get(chip_weights, chip_value, 1)
      new_accumulated = accumulated_weight + weight

      if random_value <= new_accumulated do
        {:halt, {chip_value, new_accumulated}}
      else
        {:cont, {chip_value, new_accumulated}}
      end
    end)

    selected_chip || Enum.random(chip_values)  # 备用方案
  end

  @doc """
  更新机器人的虚拟金币

  ## 参数
  - state: 房间状态
  - robot_id: 机器人ID
  - amount_change: 金币变化量（正数为增加，负数为减少）

  ## 返回
  更新后的状态
  """
  def update_robot_money(state, robot_id, amount_change) do
    case Map.get(state.players, robot_id) do
      nil -> state
      robot_player ->
        current_money = Map.get(robot_player.user_info, :money, 0)
        new_money = max(0, current_money + amount_change)

        # 如果机器人金币不足，让其退出房间
        if new_money <= 0 do
          Logger.info("🤖 [ROBOT_BROKE] 机器人金币耗尽，退出房间: #{robot_id}")
          remove_robot_from_room(state, robot_id)
        else
          updated_user_info = Map.put(robot_player.user_info, :money, new_money)
          updated_robot = %{robot_player | user_info: updated_user_info}
          updated_players = Map.put(state.players, robot_id, updated_robot)
          %{state | players: updated_players}
        end
    end
  end

  @doc """
  移除机器人

  ## 参数
  - state: 房间状态
  - robot_id: 机器人ID

  ## 返回
  更新后的状态
  """
  def remove_robot_from_room(state, robot_id) do
    # 清除机器人的下注
    updated_bets = Map.delete(state.game_data.bets, robot_id)

    # 从玩家列表移除机器人
    updated_players = Map.delete(state.players, robot_id)

    # 重新计算总下注（移除机器人下注后）
    updated_total_bets = calculate_total_bets(updated_bets)

    Logger.info("🤖 [REMOVE_ROBOT] 机器人离开房间: #{robot_id}")

    %{state |
      players: updated_players,
      game_data: %{state.game_data |
        bets: updated_bets,
        total_bets: updated_total_bets
      }
    }
  end

  # ==================== 私有辅助函数 ====================

  # 获取当前最热门的下注区域
  defp get_popular_area(total_bets) do
    if map_size(total_bets) == 0 do
      Enum.random([:long, :hu, :he])
    else
      total_bets
      |> Enum.max_by(fn {_area, amount} -> amount end)
      |> elem(0)
    end
  end

  # 基于历史趋势选择区域
  defp get_trending_area(history) do
    if length(history) < 3 do
      Enum.random([:long, :hu, :he])
    else
      # 分析最近3局的结果
      recent_results = history
      |> Enum.take(3)
      |> Enum.map(& &1.result)

      # 统计各区域出现次数
      result_counts = Enum.reduce(recent_results, %{long: 0, hu: 0, he: 0}, fn result, acc ->
        Map.update(acc, result, 1, &(&1 + 1))
      end)

      # 选择出现次数最少的区域（反向投注策略）
      result_counts
      |> Enum.min_by(fn {_area, count} -> count end)
      |> elem(0)
    end
  end

  @doc """
  清理积分不足的机器人

  ## 参数
  - state: 房间状态
  - min_money: 最低积分要求（默认1000）

  ## 返回
  清理后的状态
  """
  def cleanup_broke_robots(state, min_money \\ 1000) do
    broke_robots =
      state.players
      |> Enum.filter(fn {user_id, player} ->
        player.is_robot and
        is_integer(user_id) and user_id < 0 and
        Map.get(player.user_info, :money, 0) < min_money
      end)

    if length(broke_robots) > 0 do
      Logger.info("🤖 [CLEANUP_ROBOTS] 清理 #{length(broke_robots)} 个积分不足的机器人")

      Enum.reduce(broke_robots, state, fn {robot_id, _player}, acc_state ->
        remove_robot_from_room(acc_state, robot_id)
      end)
    else
      state
    end
  end

  @doc """
  轮换老机器人

  ## 参数
  - state: 房间状态
  - rotation_probability: 轮换概率（默认0.1，即10%）

  ## 返回
  轮换后的状态
  """
  def rotate_old_robots(state, rotation_probability \\ 0.1) do
    old_robots =
      state.players
      |> Enum.filter(fn {user_id, player} ->
        player.is_robot and
        is_integer(user_id) and user_id < 0 and
        DateTime.diff(DateTime.utc_now(), Map.get(player.user_info, :created_at, DateTime.utc_now()), :minute) > 30
      end)

    robots_to_rotate =
      old_robots
      |> Enum.filter(fn _ -> :rand.uniform() < rotation_probability end)

    if length(robots_to_rotate) > 0 do
      Logger.info("🤖 [ROTATE_ROBOTS] 轮换 #{length(robots_to_rotate)} 个老机器人")

      # 移除老机器人并添加新机器人
      state_after_removal =
        Enum.reduce(robots_to_rotate, state, fn {robot_id, _player}, acc_state ->
          remove_robot_from_room(acc_state, robot_id)
        end)

      # 添加相同数量的新机器人
      Enum.reduce(1..length(robots_to_rotate), state_after_removal, fn i, acc_state ->
        add_single_robot(acc_state, i + 1000)  # 使用不同的索引避免ID冲突
      end)
    else
      state
    end
  end

  @doc """
  统计当前机器人数量

  ## 参数
  - state: 房间状态

  ## 返回
  机器人数量
  """
  def count_robots(state) do
    state.players
    |> Enum.count(fn {user_id, player} ->
      player.is_robot and is_integer(user_id) and user_id < 0
    end)
  end

  @doc """
  生成机器人初始积分

  ## 返回
  随机积分数量
  """
  def generate_robot_initial_money do
    # 根据不同策略生成积分
    strategy = :rand.uniform(100)

    cond do
      strategy <= 30 ->
        # 30% 概率：保守型机器人，积分较少
        5000 + :rand.uniform(15000)  # 5k-20k

      strategy <= 70 ->
        # 40% 概率：中等型机器人，积分中等
        15000 + :rand.uniform(35000)  # 15k-50k

      true ->
        # 30% 概率：富豪型机器人，积分较多
        40000 + :rand.uniform(60000)  # 40k-100k
    end
  end

  # 计算总下注
  defp calculate_total_bets(bets) do
    Enum.reduce(bets, %{long: 0, hu: 0, he: 0}, fn {_user_id, user_bets}, acc ->
      Enum.reduce(user_bets, acc, fn {area, amount}, area_acc ->
        Map.update(area_acc, area, amount, &(&1 + amount))
      end)
    end)
  end
end
