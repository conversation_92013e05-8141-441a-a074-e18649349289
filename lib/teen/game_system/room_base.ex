defmodule <PERSON><PERSON>rid<PERSON>.Teen.GameSystem.RoomBase do
  @moduledoc """
  房间基础模块 - 提供通用房间逻辑的抽象

  功能：
  - 玩家进入/离开管理
  - 房间状态管理
  - 消息广播
  - 游戏生命周期管理
  - 房间日志过滤和分离
  """

  alias Cypridina.RoomSystem.RoomLogFilter

  defmacro __using__(opts) do
    quote do
      use GenServer
      require Logger

      @behaviour Cypridina.RoomSystem.RoomBehaviour

      alias Cypridina.Teen.GameSystem.{RoomManager}
      alias CypridinaWeb.GameChannel

      @registry_name :game_room_registry
      @game_type unquote(opts[:game_type])

      # 房间状态枚举
      @room_states %{
        # 等待玩家
        waiting: :waiting,
        # 游戏中
        playing: :playing,
        # 结算中
        settling: :settling,
        # 已结束
        finished: :finished
      }

      # 客户端API
      def start_link(room_spec) do
        GenServer.start_link(__MODULE__, room_spec,
          name: {:via, Registry, {@registry_name, room_spec.id}}
        )
      end

      def join_room(room_id, user_id, user_info \\ %{}) do
        RoomManager.call_room(room_id, {:join_room, user_id, user_info})
      end

      def leave_room(room_id, user_id) do
        RoomManager.call_room(room_id, {:leave_room, user_id})
      end

      def send_game_message(room_id, user_id, message) do
        RoomManager.send_to_room(room_id, {:game_message, user_id, message})
      end

      # GenServer回调
      @impl true
      def init(room_spec) do
        Logger.info("🏠 [ROOM_BASE] 初始化房间: #{room_spec.id}, 游戏类型: #{@game_type}")

        initial_state = %{
          id: room_spec.id,
          trace_id: Map.get(room_spec, :trace_id),
          game_type: @game_type,
          game_id: Map.get(room_spec, :game_id),
          topic: Map.get(room_spec, :topic),
          config: room_spec.config,
          creator_id: room_spec.creator_id,
          created_at: room_spec.created_at,
          room_state: @room_states.waiting,
          players: %{},
          max_players: Map.get(room_spec.config, :max_players, 8),
          min_players: Map.get(room_spec.config, :min_players, 2),
          game_data: %{},
          last_activity: DateTime.utc_now()
        }

        # 调用具体游戏的初始化逻辑
        state = init_game_logic(initial_state)

        # 设置房间超时检查
        # schedule_timeout_check()

        # 日志
        # 设置Logger metadata，包含room_id
        room_id = room_spec.id
        Logger.metadata(room_id: room_id, game_id: initial_state.game_id)

        # 创建房间专用日志过滤器
        case RoomLogFilter.create_room_logger(%{game_id: initial_state.game_id, room_id: room_id}) do
          {:ok, _handler_id} ->
            Logger.info("🏠 [ROOM_BASE] 房间日志过滤器创建成功: #{room_id}")

          {:error, reason} ->
            Logger.warning("🏠 [ROOM_BASE] 房间日志过滤器创建失败: #{room_id}, 原因: #{inspect(reason)}")
        end

        {:ok, state}
      end

      @impl true
      def handle_call(:get_state, _from, state) do
        {:reply, state, state}
      end

      @impl true
      def handle_call({:join_room, user_id, user_info}, _from, state) do
        {result, new_state} = handle_player_join(state, user_id, user_info)
        {:reply, result, new_state}
      end

      @impl true
      def handle_call({:leave_room, user_id}, _from, state) do
        {result, new_state} = handle_player_leave(state, user_id)
        {:reply, result, new_state}
      end

      @impl true
      def handle_cast({:game_message, user_id, message}, state) do
        new_state = handle_game_message(state, user_id, message)
        {:noreply, new_state}
      end

      @impl true
      def handle_cast({:broadcast, message}, state) do
        broadcast_to_room(state, message)
        {:noreply, state}
      end

      @impl true
      def handle_info(:timeout_check, state) do
        new_state = check_room_timeout(state)
        schedule_timeout_check()
        {:noreply, new_state}
      end

      @impl true
      def handle_info(:game_tick, state) do
        new_state = handle_game_tick(state)
        {:noreply, new_state}
      end

      @impl true
      def handle_info(:close_room, state) do
        Logger.info("🏠 [ROOM_BASE] 关闭房间: #{state.id}")
        {:stop, :normal, state}
      end

      @impl true
      def terminate(reason, state) do
        Logger.warning("🏠 [ROOM_BASE] 房间进程终止: #{state.id}, 原因: #{inspect(reason)}")

        # 广播房间销毁事件给room_manager
        Phoenix.PubSub.broadcast(
          Cypridina.PubSub,
          "room_events",
          {:room_terminated, state.id, reason}
        )

        # 确保清理房间日志过滤器
        RoomLogFilter.remove_room_logger(state.id)

        :ok
      end

      @impl true
      def handle_info(:start_new_round, state) do
        new_state = on_game_start(state)
        {:noreply, new_state}
      end

      # 通用房间逻辑

      defp handle_player_join(state, user_id, user_info) do
        cond do
          Map.has_key?(state.players, user_id) ->
            Logger.info("🏠 [ROOM_BASE] 玩家重新加入: #{user_id}")

            new_state =
              state
              |> update_player_info(user_id, user_info)
              # 调用具体游戏的玩家加入逻辑
              |> on_player_rejoined(user_id, user_info)

            {{:ok, :rejoined}, new_state}

          map_size(state.players) >= state.max_players ->
            Logger.warning("🏠 [ROOM_BASE] 房间已满: #{state.id}")
            {{:error, :room_full}, state}

          state.room_state not in [@room_states.waiting, @room_states.playing] ->
            Logger.warning("🏠 [ROOM_BASE] 房间状态不允许加入: #{state.room_state}")
            {{:error, :invalid_room_state}, state}

          true ->
            Logger.info("🏠 [ROOM_BASE] 玩家加入房间: #{user_id} -> #{state.id}")

            player_data = %{
              user_id: user_id,
              user_info: user_info,
              joined_at: DateTime.utc_now(),
              is_ready: false,
              is_robot: false
            }

            new_state = %{
              state
              | players: Map.put(state.players, user_id, player_data),
                last_activity: DateTime.utc_now()
            }

            # 调用具体游戏的玩家加入逻辑
            new_state = on_player_joined(new_state, user_id, user_info)

            # 检查是否可以开始游戏
            new_state = check_game_start(new_state)

            {{:ok, :joined}, new_state}
        end
      end

      defp handle_player_leave(state, user_id) do
        if Map.has_key?(state.players, user_id) do
          Logger.info("🏠 [ROOM_BASE] 玩家离开房间: #{user_id} <- #{state.id}")

          new_state = %{
            state
            | players: Map.delete(state.players, user_id),
              last_activity: DateTime.utc_now()
          }

          # 调用具体游戏的玩家离开逻辑
          new_state = on_player_left(new_state, user_id)

          # 检查房间是否应该关闭
          new_state = check_room_close(new_state)

          {{:ok, :left}, new_state}
        else
          Logger.warning("🏠 [ROOM_BASE] 玩家不在房间中: #{user_id}")
          {{:error, :not_in_room}, state}
        end
      end

      defp update_player_info(state, user_id, user_info) do
        case Map.get(state.players, user_id) do
          nil ->
            state

          player_data ->
            updated_player = %{player_data | user_info: user_info}
            %{state | players: Map.put(state.players, user_id, updated_player)}
        end
      end

      defp check_game_start(state) do
        player_count = map_size(state.players)

        if state.room_state == @room_states.waiting and player_count >= state.min_players do
          Logger.info("🏠 [ROOM_BASE] 房间满足开始条件: #{state.id}, 玩家数: #{player_count}")
          start_game(state)
        else
          state
        end
      end

      defp check_room_close(state) do
        if map_size(state.players) == 0 do
          Logger.info("🏠 [ROOM_BASE] 房间无玩家，准备关闭: #{state.id}")
          schedule_room_close()
          %{state | room_state: @room_states.finished}
        else
          state
        end
      end

      defp check_room_timeout(state) do
        timeout_minutes = 30
        timeout_threshold = DateTime.add(DateTime.utc_now(), -timeout_minutes * 60, :second)

        if DateTime.compare(state.last_activity, timeout_threshold) == :lt do
          Logger.info("🏠 [ROOM_BASE] 房间超时，准备关闭: #{state.id}")
          schedule_room_close()
          %{state | room_state: @room_states.finished}
        else
          state
        end
      end

      # ==================== 统一消息发送系统 ====================

      @doc """
      统一的单用户消息发送 - 通过GameChannel转发
      """
      defp send_to_user(state, user_id, message) do
        CypridinaWeb.Endpoint.broadcast!("user:#{user_id}", "private_message", message)
      end

      @doc """
      统一的房间广播消息发送 - 通过GameChannel转发
      """
      defp broadcast_to_room(state, message) do
        topic = state.topic
        Logger.info("广播消息 #{inspect(topic)}")
        CypridinaWeb.Endpoint.broadcast!(topic, "room_message", message)
      end

      @doc """
      统一的房间广播消息发送 - 排除指定玩家
      """
      defp broadcast_to_room(state, message, exclude_user_id) do
        topic = state.topic
        Logger.info("广播消息 #{inspect(topic)}, 排除玩家: #{exclude_user_id}")

        # 添加排除玩家信息到消息中
        message_with_exclude = Map.put(message, "_exclude_user_id", exclude_user_id)
        CypridinaWeb.Endpoint.broadcast!(topic, "room_message_exclude", message_with_exclude)
      end

      defp schedule_timeout_check() do
        # 5分钟检查一次
        Process.send_after(self(), :timeout_check, 5 * 60 * 1000)
      end

      defp schedule_room_close() do
        # 10秒后关闭
        Process.send_after(self(), :close_room, 10 * 1000)
      end

      defp start_game(state) do
        Logger.info("🏠 [ROOM_BASE] 开始游戏: #{state.id}")
        new_state = %{state | room_state: @room_states.playing}

        # 调用具体游戏的开始逻辑
        on_game_start(new_state)
      end

      # 需要在具体游戏中实现的回调函数
      def init_game_logic(state), do: state
      def on_player_joined(state, _user_id, _user_info), do: state
      def on_player_rejoined(state, _user_id, _user_info), do: state
      def on_player_left(state, _user_id), do: state
      def on_game_start(state), do: state
      def handle_game_message(state, _user_id, _message), do: state
      def handle_game_tick(state), do: state

      defoverridable init_game_logic: 1,
                     on_player_joined: 3,
                     on_player_rejoined: 3,
                     on_player_left: 2,
                     on_game_start: 1,
                     handle_game_message: 3,
                     handle_game_tick: 1
    end
  end
end
