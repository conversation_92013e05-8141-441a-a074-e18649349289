defmodule <PERSON>prid<PERSON>.Accounts.AgentService do
  @moduledoc """
  代理系统核心业务逻辑

  ## 代理级别系统
  - `agent_level = -1`: 不是代理（普通用户）
  - `agent_level = 0`: 根代理
  - `agent_level > 0`: 下级代理，每发展一级 +1

  ## 主要功能
  - 代理级别检查
  - 代理关系管理
  - 权限验证
  - 抽水处理
  """

  alias Cypridina.Accounts.{User, AgentRelationship}
  alias <PERSON>pridina.Accounts
  require Logger
  require Ash.Query

  # ==================== 代理级别检查函数 ====================

  @doc """
  检查用户是否为代理

  ## 参数
  - `user`: 用户结构体或用户ID

  ## 返回值
  - `true`: 用户是代理（agent_level >= 0）
  - `false`: 用户不是代理（agent_level = -1）
  """
  def is_agent?(%User{agent_level: agent_level}), do: agent_level >= 0

  def is_agent?(user_id) when is_binary(user_id) do
    case get_user(user_id) do
      {:ok, user} -> is_agent?(user)
      _ -> false
    end
  end

  def is_agent?(_), do: false

  @doc """
  检查用户是否为根代理

  ## 参数
  - `user`: 用户结构体或用户ID

  ## 返回值
  - `true`: 用户是根代理（agent_level = 0）
  - `false`: 用户不是根代理
  """
  def is_root_agent?(%User{agent_level: 0}), do: true
  def is_root_agent?(%User{agent_level: _}), do: false

  def is_root_agent?(user_id) when is_binary(user_id) do
    case get_user(user_id) do
      {:ok, user} -> is_root_agent?(user)
      _ -> false
    end
  end

  def is_root_agent?(_), do: false

  @doc """
  检查用户是否具有指定的代理级别或更高级别

  ## 参数
  - `user`: 用户结构体或用户ID
  - `required_level`: 要求的代理级别

  ## 返回值
  - `true`: 用户具有指定级别或更高级别
  - `false`: 用户不具有指定级别
  """
  def has_agent_level?(%User{agent_level: agent_level}, required_level) when agent_level >= 0 do
    # 级别数字越小，权限越高
    agent_level <= required_level
  end

  def has_agent_level?(%User{agent_level: -1}, _required_level), do: false

  def has_agent_level?(user_id, required_level) when is_binary(user_id) do
    case get_user(user_id) do
      {:ok, user} -> has_agent_level?(user, required_level)
      _ -> false
    end
  end

  def has_agent_level?(_, _), do: false

  @doc """
  获取用户的代理级别

  ## 参数
  - `user`: 用户结构体或用户ID

  ## 返回值
  - 代理级别数字，-1 表示不是代理
  """
  def get_agent_level(%User{agent_level: agent_level}), do: agent_level

  def get_agent_level(user_id) when is_binary(user_id) do
    case get_user(user_id) do
      {:ok, user} -> get_agent_level(user)
      _ -> -1
    end
  end

  def get_agent_level(_), do: -1

  @doc """
  检查是否存在根代理

  ## 返回值
  - `true`: 存在根代理
  - `false`: 不存在根代理
  """
  def root_agent_exists? do
    case User
         |> Ash.Query.filter(agent_level: 0)
         |> Ash.Query.limit(1)
         |> Ash.read() do
      {:ok, []} -> false
      {:ok, _} -> true
      _ -> false
    end
  end

  @doc """
  检查用户是否可以访问后台管理

  只有代理和管理员才能访问后台管理

  ## 参数
  - `user`: 用户结构体

  ## 返回值
  - `true`: 可以访问
  - `false`: 不能访问
  """
  def can_access_admin?(%User{} = user) do
    # 检查是否为管理员
    # 检查是否为代理
    Cypridina.Accounts.AdminService.is_admin?(user) or
      is_agent?(user)
  end

  def can_access_admin?(_), do: false

  @doc """
  检查代理是否可以管理指定用户

  代理只能管理自己的直接下级

  ## 参数
  - `agent`: 代理用户结构体
  - `target_user_id`: 目标用户ID

  ## 返回值
  - `true`: 可以管理
  - `false`: 不能管理
  """
  def can_manage_user?(%User{} = agent, target_user_id) do
    # 管理员可以管理所有用户
    if Cypridina.Accounts.AdminService.is_admin?(agent) do
      true
    else
      # 代理只能管理自己的直接下级
      agent.id
      |> get_direct_subordinates()
      |> Enum.any?(fn subordinate -> subordinate.id == target_user_id end)
    end
  end

  def can_manage_user?(_, _), do: false

  @doc """
  获取用户的直接下级代理

  ## 参数
  - `agent_id`: 代理用户ID

  ## 返回值
  - 直接下级代理列表
  """
  def get_direct_subordinates(agent_id) do
    # 通过 AgentRelationship 获取直接下级
    case AgentRelationship
         |> Ash.Query.filter(agent_id: agent_id, status: 1)
         |> Ash.Query.load(:subordinate)
         |> Ash.read() do
      {:ok, relationships} -> Enum.map(relationships, & &1.subordinate)
      _ -> []
    end
  end

  # ==================== 原有业务逻辑函数 ====================

  @doc """
  代理创建下线账号
  """
  def create_subordinate_account(agent_id, params) do
    with {:ok, agent} <- get_agent(agent_id),
         {:ok, user} <- create_user_for_agent(agent, params),
         {:ok, relationship} <- create_agent_relationship(agent_id, user.id, params) do
      {:ok, %{user: user, relationship: relationship}}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  设置代理对下线的抽水比例
  """
  def set_commission_rate(agent_id, subordinate_id, commission_rate) do
    with {:ok, relationship} <- get_agent_relationship(agent_id, subordinate_id) do
      relationship
      |> Ash.Changeset.for_update(:update, %{commission_rate: commission_rate})
      |> Ash.update()
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  获取代理的所有下线
  """
  def get_agent_subordinates(agent_id) do
    AgentRelationship.get_by_agent!(%{agent_id: agent_id})
    |> Ash.load([:subordinate])
  end

  @doc """
  获取用户的代理关系树
  """
  def get_agent_tree(agent_id) do
    AgentRelationship.get_agent_tree!(%{agent_id: agent_id})
    |> Ash.load([:subordinate, :agent])
  end

  @doc """
  处理投注时的抽水
  """
  def process_bet_commission(%{id: id, payout: payout, user_id: user_id} = bet) do
    case get_user_agent(user_id) do
      {:ok, agent_relationship} ->
        commission_rate = agent_relationship.commission_rate
        commission_amount = calculate_commission(payout, commission_rate)
        agent_id = agent_relationship.agent_id

        if commission_amount > 0 do
          # 抽水记录通过 PointsTransactionHelper 记录

          # 获取代理当前积分
          balance_before = Accounts.get_user_points(agent_relationship.agent_id)

          # 给代理增加积分
          case Accounts.add_points(agent_relationship.agent_id, commission_amount) do
            {:ok, _asset} ->
              # 获取增加后的积分
              balance_after = Accounts.get_user_points(agent_relationship.agent_id)

              # 记录抽水的积分变动
              RacingGame.PointsTransactionHelper.record_commission(
                agent_relationship.agent_id,
                commission_amount,  # 正数表示收入
                Decimal.new(balance_before),
                Decimal.new(balance_after),
                [
                  commission_type: "bet",
                  original_transaction_id: id,
                  commission_rate: commission_rate,
                  original_amount: payout,
                  from_user_id: user_id,
                  from_username: get_username(user_id)
                ]
              )

            {:error, reason} ->
              Logger.error("增加抽水积分失败: #{inspect(reason)}")
          end

          Logger.info(
            "投注抽水成功: 代理#{agent_relationship.agent_id} 从下线#{bet.user_id} 抽取#{commission_amount}积分"
          )
        end

      {:error, :no_agent} ->
        # 用户没有代理，不需要抽水
        :ok

      {:error, reason} ->
        Logger.error("处理投注抽水失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  处理股票交易时的抽水
  """
  def process_stock_commission(user_id, transaction_type, transaction_id, amount) do
    case get_user_agent(user_id) do
      {:ok, agent_relationship} ->
        commission_amount = calculate_commission(amount, agent_relationship.commission_rate)

        if commission_amount > 0 do
          # 抽水记录通过 PointsTransactionHelper 记录

          # 获取代理当前积分
          balance_before = Accounts.get_user_points(agent_relationship.agent_id)

          # 给代理增加积分
          case Accounts.add_points(agent_relationship.agent_id, commission_amount) do
            {:ok, _asset} ->
              # 获取增加后的积分
              balance_after = Accounts.get_user_points(agent_relationship.agent_id)

              # 记录抽水的积分变动
              RacingGame.PointsTransactionHelper.record_commission(
                agent_relationship.agent_id,
                commission_amount,  # 正数表示收入
                Decimal.new(balance_before),
                Decimal.new(balance_after),
                [
                  commission_type: transaction_type,
                  original_transaction_id: transaction_id,
                  commission_rate: agent_relationship.commission_rate,
                  original_amount: amount,
                  from_user_id: user_id,
                  from_username: get_username(user_id)
                ]
              )

            {:error, reason} ->
              Logger.error("增加抽水积分失败: #{inspect(reason)}")
          end

          Logger.info(
            "股票交易抽水成功: 代理#{agent_relationship.agent_id} 从下线#{user_id} 抽取#{commission_amount}积分"
          )
        end

      {:error, :no_agent} ->
        # 用户没有代理，不需要抽水
        :ok

      {:error, reason} ->
        Logger.error("处理股票交易抽水失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  获取代理的收益统计
  注意：CommissionRecord 已被删除，此函数需要重新实现或移除
  """
  def get_agent_earnings(_agent_id, _start_date \\ nil, _end_date \\ nil) do
    # TODO: 重新实现代理收益统计，可能通过 PointsTransactionHelper 实现
    {:error, "CommissionRecord 已被删除，功能需要重新实现"}
  end

  @doc """
  获取用户的代理关系（公共函数）
  """
  def get_user_agent(user_id) do
    case AgentRelationship.get_by_subordinate!(%{subordinate_id: user_id}) do
      [relationship] -> {:ok, relationship}
      [] -> {:error, :no_agent}
    end
  end

  # 私有函数
  defp get_agent(agent_id) do
    case User |> Ash.get(agent_id) do
      {:ok, user} ->
        if is_agent?(user) do
          {:ok, user}
        else
          {:error, :not_agent}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp create_user_for_agent(agent, params) do
    # 新创建的用户默认不是代理（agent_level = -1）
    user_params =
      params
      # |> Map.put_new(:subordinate_relationship, %{
      #   agent_id: agent.id,
      #   commission_rate: params.commission_rate,
      #   level: 0
      # })
      |> Map.merge( %{
        # 普通用户
        permission_level: 0,
        created_by_agent: agent.id,
      })
      |> Map.drop([:commission_rate, "commission_rate"])

    User
    |> Ash.Changeset.for_create(:register_with_username, user_params)
    |> Ash.create()
  end

  defp create_agent_relationship(agent_id, subordinate_id, params) do
    commission_rate = Map.get(params, :commission_rate, Decimal.new("0.05"))
    commission_rate = Map.get(params, "commission_rate", commission_rate)

    # 处理字符串格式的抽水比例
    commission_rate = case commission_rate do
      rate when is_binary(rate) ->
        # 将百分比转换为小数（例如 "5.00" -> 0.05）
        {float_rate, _} = Float.parse(rate)
        Decimal.div(Decimal.from_float(float_rate), 100)
      rate when is_float(rate) ->
        Decimal.div(Decimal.from_float(rate), 100)
      rate when is_integer(rate) ->
        # 处理整数输入
        Decimal.div(Decimal.new(rate), 100)
      %Decimal{} = rate -> rate
      _ -> Decimal.new("0.05")
    end

    case AgentRelationship
         |> Ash.Changeset.for_create(:create, %{
           agent_id: agent_id,
           subordinate_id: subordinate_id,
           commission_rate: commission_rate
         })
         |> Ash.create() do
      {:ok, relationship} -> {:ok, relationship}
      {:error, reason} -> {:error, reason}
    end
  end

  defp get_agent_relationship(agent_id, subordinate_id) do
    case AgentRelationship
         |> Ash.Query.filter(agent_id == ^agent_id and subordinate_id == ^subordinate_id and status == 1)
         |> Ash.read_one() do
      {:ok, relationship} when not is_nil(relationship) -> {:ok, relationship}
      {:ok, nil} -> {:error, :relationship_not_found}
      {:error, reason} -> {:error, reason}
    end
  end

  # 私有函数已经被公共函数替代，删除重复定义

  defp calculate_commission(amount, commission_rate) do
    # 计算抽水金额，向下取整
    amount
    |> Decimal.mult(commission_rate)
    |> Decimal.round(0, :down)  # 向下取整到整数
    |> Decimal.to_integer()
  end

  # create_commission_record 函数已删除，因为 CommissionRecord 已被移除

  defp get_user(user_id) do
    User
    |> Ash.get(user_id)
  end

  @doc """
  获取用户名
  """
  def get_username(user_id) do
    case get_user(user_id) do
      {:ok, user} -> user.username || "未知用户"
      _ -> "未知用户"
    end
  end

  @doc """
  获取代理的下级用户数量
  """
  def get_subordinate_count(agent) do
    if is_agent?(agent) do
      # 获取直接下级数量
      count = agent.id |> get_direct_subordinates() |> length()
      {:ok, count}
    else
      {:ok, 0}
    end
  end

  @doc """
  获取代理的下级代理数量
  """
  def get_subordinate_agent_count(agent) do
    if is_agent?(agent) do
      # 获取下级中是代理的数量
      subordinates = get_direct_subordinates(agent.id)
      agent_count = Enum.count(subordinates, &is_agent?/1)
      {:ok, agent_count}
    else
      {:ok, 0}
    end
  end

  @doc """
  获取代理的佣金金额
  """
  def get_user_commission(agent) do
    if is_agent?(agent) do
      # 获取本月佣金总额
      start_date = Date.beginning_of_month(Date.utc_today())
      end_date = Date.end_of_month(Date.utc_today())

      case get_agent_earnings(agent.id, start_date, end_date) do
        {:ok, earnings} ->
          total =
            earnings
            |> Enum.reduce(Decimal.new(0), fn earning, acc ->
              Decimal.add(acc, earning.commission_amount)
            end)
            |> Decimal.to_float()

          {:ok, total}

        _ ->
          {:ok, 0.0}
      end
    else
      {:ok, 0.0}
    end
  end


end
