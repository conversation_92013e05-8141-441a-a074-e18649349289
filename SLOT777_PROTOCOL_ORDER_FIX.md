# Slot777协议发送顺序修正

## 🎯 问题描述

根据前端要求，slot777免费游戏的协议发送顺序需要调整：
- **修改前**：同时发送1001和1002协议
- **修改后**：先发送1001协议（正常游戏结果），然后发送1002协议（免费游戏结果）

## ✅ 解决方案

### 1. 协议发送顺序
```
正常游戏流程：
1. 玩家发起游戏请求
2. 服务端生成游戏结果
3. 先发送1001协议（SC_SLOT777_GAMESTART_P）- 正常游戏结果
4. 如果触发免费游戏，再发送1002协议（SC_SLOT777_FREEGAME_P）- 免费游戏结果
```

### 2. 实现机制

#### 2.1 延迟发送机制
在`handle_call({:start_game, user_id, odds}, _from, state)`中：
- 不立即发送免费游戏结果
- 将免费游戏信息存储到`pending_free_games`中
- 在`handle_game_start_request`中按正确顺序发送

#### 2.2 状态管理
添加`pending_free_games`字段到游戏数据中：
```elixir
game_data = %{
  # ... 其他字段
  # 待发送的免费游戏
  pending_free_games: %{},
  # ...
}
```

#### 2.3 免费游戏信息存储
```elixir
free_game_info = %{
  user_id: user_id,
  free_times: game_result["freetimes"],
  odds: odds
}
put_in(new_state, [:game_data, :pending_free_games, user_id], free_game_info)
```

### 3. 修改的关键函数

#### 3.1 handle_call({:start_game, ...})
**修改前**：
```elixir
# 检查是否触发免费游戏
if game_result["freetimes"] > 0 do
  # 立即生成免费游戏结果并发送
  generate_and_send_free_games(new_state, user_id, game_result["freetimes"], odds)
end
```

**修改后**：
```elixir
# 存储免费游戏信息到状态中，稍后发送
updated_state = if game_result["freetimes"] > 0 do
  free_game_info = %{
    user_id: user_id,
    free_times: game_result["freetimes"],
    odds: odds
  }
  put_in(new_state, [:game_data, :pending_free_games, user_id], free_game_info)
else
  new_state
end
```

#### 3.2 handle_game_start_request
**修改后的发送顺序**：
```elixir
# 1. 先发送1001协议 - 正常游戏结果
response_1001 = %{
  "mainId" => 5,
  "subId" => 1001,  # SC_SLOT777_GAMESTART_P
  "data" => result
}
send_to_user(new_state, user_id, response_1001)

# 2. 检查是否有待发送的免费游戏，如果有则发送1002协议
final_state = case get_in(new_state, [:game_data, :pending_free_games, user_id]) do
  %{free_times: free_times, odds: free_odds} ->
    # 生成并发送免费游戏结果
    generate_and_send_free_games(new_state, user_id, free_times, free_odds)
    # 清除待发送的免费游戏信息
    put_in(new_state, [:game_data, :pending_free_games], 
           Map.delete(new_state.game_data.pending_free_games || %{}, user_id))
  _ ->
    new_state
end
```

## 🔄 工作流程

### 1. 正常游戏（无免费游戏）
```
客户端请求 → 生成游戏结果 → 发送1001协议 → 完成
```

### 2. 触发免费游戏
```
客户端请求 → 生成游戏结果 → 存储免费游戏信息 → 
发送1001协议 → 检查待发送免费游戏 → 发送1002协议 → 清除免费游戏信息 → 完成
```

## 📋 协议时序图

```
客户端                    服务端
   |                        |
   |-- 游戏开始请求 -------->|
   |                        |-- 生成游戏结果
   |                        |-- 检查免费游戏触发
   |                        |-- 存储免费游戏信息
   |<------ 1001协议 -------|   (正常游戏结果)
   |                        |
   |                        |-- 检查待发送免费游戏
   |<------ 1002协议 -------|   (免费游戏结果)
   |                        |-- 清除免费游戏信息
   |                        |
```

## 🎯 关键优势

### 1. 协议顺序正确
- 严格按照前端要求的顺序发送协议
- 确保前端能正确处理游戏流程

### 2. 状态管理清晰
- 使用`pending_free_games`临时存储免费游戏信息
- 发送完成后立即清理，避免状态污染

### 3. 代码结构清晰
- 分离了游戏逻辑和协议发送逻辑
- 便于维护和调试

## 🧪 测试要点

### 1. 协议顺序验证
- 确认1001协议先于1002协议发送
- 验证协议间的时间间隔合理

### 2. 状态管理测试
- 测试免费游戏信息的正确存储和清理
- 验证多玩家同时触发免费游戏的处理

### 3. 边界情况测试
- 测试连续触发免费游戏的情况
- 验证网络异常时的状态恢复

## ✅ 总结

成功修正了slot777免费游戏的协议发送顺序：
- **正确的时序**：先发送1001协议，再发送1002协议
- **清晰的状态管理**：使用pending_free_games临时存储
- **完整的流程控制**：确保协议按正确顺序发送
- **良好的代码结构**：便于维护和扩展

现在slot777的免费游戏功能完全符合前端的协议处理要求！🎰✨
