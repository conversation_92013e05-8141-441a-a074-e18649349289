# 测试动态机器人管理系统
defmodule TestRobotManagement do
  @moduledoc """
  测试龙虎斗游戏的动态机器人管理系统
  """

  alias Cypridina.Teen.GameSystem.Games.LongHu.LongHuAI

  def test_robot_management do
    IO.puts("🧪 开始测试动态机器人管理系统")
    
    # 创建模拟游戏状态
    state = create_mock_game_state()
    
    # 测试1: 添加机器人
    IO.puts("\n=== 测试1: 添加机器人 ===")
    test_add_robots(state)
    
    # 测试2: 清理积分不足的机器人
    IO.puts("\n=== 测试2: 清理积分不足的机器人 ===")
    test_cleanup_broke_robots()
    
    # 测试3: 机器人轮换
    IO.puts("\n=== 测试3: 机器人轮换 ===")
    test_robot_rotation()
    
    # 测试4: 动态管理
    IO.puts("\n=== 测试4: 动态管理 ===")
    test_dynamic_management()
    
    # 测试5: 积分生成
    IO.puts("\n=== 测试5: 积分生成 ===")
    test_money_generation()
    
    IO.puts("\n✅ 所有测试完成")
  end

  defp create_mock_game_state do
    %{
      id: "test_room_001",
      players: %{},
      game_data: %{
        config: %{
          robot_count: 8,
          enable_robots: true
        }
      }
    }
  end

  defp test_add_robots(state) do
    # 测试添加机器人
    new_state = LongHuAI.add_robots_if_needed(state)
    robot_count = LongHuAI.count_robots(new_state)
    
    IO.puts("初始机器人数量: #{robot_count}")
    IO.puts("目标机器人数量: #{state.game_data.config.robot_count}")
    
    if robot_count == state.game_data.config.robot_count do
      IO.puts("✅ 机器人添加测试通过")
    else
      IO.puts("❌ 机器人添加测试失败")
    end
    
    # 显示机器人信息
    new_state.players
    |> Enum.filter(fn {user_id, player} -> 
      player.is_robot and is_integer(user_id) and user_id < 0 
    end)
    |> Enum.take(3)
    |> Enum.each(fn {robot_id, robot} ->
      IO.puts("机器人 #{robot_id}: #{robot.user_info.nickname} - 积分: #{robot.user_info.money}")
    end)
  end

  defp test_cleanup_broke_robots do
    # 创建包含积分不足机器人的状态
    state_with_broke_robots = %{
      id: "test_room_002",
      players: %{
        -1000001 => %{
          is_robot: true,
          user_info: %{nickname: "穷机器人1", money: 100}  # 积分不足
        },
        -1000002 => %{
          is_robot: true,
          user_info: %{nickname: "富机器人", money: 5000}   # 积分充足
        },
        -1000003 => %{
          is_robot: true,
          user_info: %{nickname: "穷机器人2", money: 50}   # 积分不足
        }
      },
      game_data: %{
        bets: %{},
        total_bets: %{long: 0, hu: 0, he: 0}
      }
    }
    
    IO.puts("清理前机器人数量: #{LongHuAI.count_robots(state_with_broke_robots)}")
    
    # 清理积分不足的机器人（最低要求1000积分）
    cleaned_state = LongHuAI.cleanup_broke_robots(state_with_broke_robots, 1000)
    
    IO.puts("清理后机器人数量: #{LongHuAI.count_robots(cleaned_state)}")
    
    # 检查剩余的机器人
    remaining_robots = cleaned_state.players
    |> Enum.filter(fn {user_id, player} -> 
      player.is_robot and is_integer(user_id) and user_id < 0 
    end)
    
    IO.puts("剩余机器人:")
    Enum.each(remaining_robots, fn {robot_id, robot} ->
      IO.puts("  #{robot_id}: #{robot.user_info.nickname} - 积分: #{robot.user_info.money}")
    end)
    
    if length(remaining_robots) == 1 do
      IO.puts("✅ 积分不足机器人清理测试通过")
    else
      IO.puts("❌ 积分不足机器人清理测试失败")
    end
  end

  defp test_robot_rotation do
    # 创建包含老机器人的状态
    old_time = DateTime.add(DateTime.utc_now(), -40, :minute)  # 40分钟前
    
    state_with_old_robots = %{
      id: "test_room_003",
      players: %{
        -1000001 => %{
          is_robot: true,
          user_info: %{
            nickname: "老机器人1", 
            money: 5000,
            created_at: old_time  # 老机器人
          }
        },
        -1000002 => %{
          is_robot: true,
          user_info: %{
            nickname: "新机器人", 
            money: 3000,
            created_at: DateTime.utc_now()  # 新机器人
          }
        }
      },
      game_data: %{
        bets: %{},
        total_bets: %{long: 0, hu: 0, he: 0}
      }
    }
    
    IO.puts("轮换前机器人:")
    state_with_old_robots.players
    |> Enum.filter(fn {user_id, player} -> 
      player.is_robot and is_integer(user_id) and user_id < 0 
    end)
    |> Enum.each(fn {robot_id, robot} ->
      age_minutes = DateTime.diff(DateTime.utc_now(), Map.get(robot.user_info, :created_at, DateTime.utc_now()), :minute)
      IO.puts("  #{robot_id}: #{robot.user_info.nickname} - 年龄: #{age_minutes}分钟")
    end)
    
    # 强制轮换（100%概率）
    rotated_state = LongHuAI.rotate_old_robots(state_with_old_robots, 1.0)
    
    IO.puts("轮换后机器人数量: #{LongHuAI.count_robots(rotated_state)}")
    IO.puts("✅ 机器人轮换测试完成")
  end

  defp test_dynamic_management do
    # 创建混合状态：有积分不足的、有老的、数量不足
    state = %{
      id: "test_room_004",
      players: %{
        -1000001 => %{
          is_robot: true,
          user_info: %{nickname: "穷机器人", money: 100}  # 积分不足，会被清理
        }
      },
      game_data: %{
        config: %{robot_count: 5, enable_robots: true},
        bets: %{},
        total_bets: %{long: 0, hu: 0, he: 0}
      }
    }
    
    IO.puts("动态管理前:")
    IO.puts("  机器人数量: #{LongHuAI.count_robots(state)}")
    IO.puts("  目标数量: #{state.game_data.config.robot_count}")
    
    # 执行动态管理
    managed_state = LongHuAI.manage_robots_dynamically(state)
    
    IO.puts("动态管理后:")
    IO.puts("  机器人数量: #{LongHuAI.count_robots(managed_state)}")
    
    if LongHuAI.count_robots(managed_state) == state.game_data.config.robot_count do
      IO.puts("✅ 动态管理测试通过")
    else
      IO.puts("❌ 动态管理测试失败")
    end
  end

  defp test_money_generation do
    IO.puts("测试机器人积分生成:")
    
    # 生成10个机器人的积分
    money_samples = Enum.map(1..10, fn _ -> 
      LongHuAI.generate_robot_initial_money()
    end)
    
    IO.puts("积分样本: #{inspect(money_samples)}")
    
    min_money = Enum.min(money_samples)
    max_money = Enum.max(money_samples)
    avg_money = Enum.sum(money_samples) / length(money_samples)
    
    IO.puts("最小积分: #{min_money}")
    IO.puts("最大积分: #{max_money}")
    IO.puts("平均积分: #{round(avg_money)}")
    
    if min_money >= 5000 and max_money <= 100000 do
      IO.puts("✅ 积分生成测试通过")
    else
      IO.puts("❌ 积分生成测试失败")
    end
  end
end

# 运行测试
TestRobotManagement.test_robot_management()
