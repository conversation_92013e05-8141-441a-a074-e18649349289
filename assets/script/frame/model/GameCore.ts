import { GameEvent, NO_RECHARGE_USER_RATE, NOTICE_END_TIME, NOTICE_START_TIME, RECHARGE_USER_RATE, SceneName } from "../common/Define";
import { TextTips } from "../common/Language";
import { MainProto, Game, XC, NoticeManager, HallActivity } from "../common/Protocol";
import Common from "../common/Common";
import AlertHelper from "../extentions/AlertHelper";
import ToastHelper from "../extentions/ToastHelper";
import DataManager from "../manager/DataManager";
import EventManager from "../manager/EventManager";
import SceneManager from "../manager/SceneManager";
import GameManager from "../manager/GameManager";
import UIHelper from "../extentions/UIHelper";
import WaitHelper from "../extentions/WaitHelper";
import Config from "../config/Config";

//////////////////////////////////////////////////////////////////////////////////
export default class GameCore extends cc.Component {
    //////////////////////////////////////////////////////////////////////////////
    // 迷你游戏对象
    public isMiniGame: boolean = false;
    // 游戏资源名称
    public bundleName: string = "";
    // 房间的基础信息
    public roomInfo: any = null;
    // 用户的基础信息
    public userInfo: any = null;
    // 登录房间信息
    public loginInfo: any = null;
    // 游戏网络服务对象
    public netService: any = null;
    // 玩家标识ID
    public playerid: number = -1;
    // 我的座位ID
    public mySeatId: number = -1;
    // 游戏的最大玩家数
    public maxPlayers: number = 0;
    // 是否已参与游戏
    public isGaming: boolean = false;
    // 房间的状态
    public roomState: number = 0;
    // chat fee
    public singlechatfee: number = 0;
    public tipdealerfee: number = 0;

    public noticeInterVal : any
    //////////////////////////////////////////////////////////////////////////////
    onLoad() {
        //非充值玩家
        if (DataManager.instance.ischarge === 0){
            Config.SCORE_RATE = NO_RECHARGE_USER_RATE
        }else {
            Config.SCORE_RATE = RECHARGE_USER_RATE
        }
        if (this.isMiniGame) {
            GameManager.miniInstance.bindGameCore(this);
        }
        else {
            GameManager.instance.bindGameCore(this);
        }
        SceneManager.instance.updateSafeArea();

        cc.systemEvent.on(cc.SystemEvent.EventType.KEY_DOWN, this.onKeyDown, this);
        cc.game.on(cc.game.EVENT_HIDE, this.onEnterBackground, this);
        cc.game.on(cc.game.EVENT_SHOW, this.onEnterForgeground, this);
        this.noticeInterVal = setInterval(() => {
            let infoData = Common.getNoticeData()
            SceneManager.instance.showNotice(infoData);
        },  Common.getRandomNumber(NOTICE_START_TIME ,NOTICE_END_TIME));
    }
    onKeyDown (event) {
        switch (event.keyCode) {
            case cc.macro.KEY.back:
                // 处理 Android 返回按键
                this.onBackPressed();
                break;
        }
    }
    protected onEnable(): void {
        cc.director.preloadScene(SceneName.Hall,function () {
            // 场景加载完成后的回调
            cc.log('大厅场景 预定加载完成');
        });
    }

    onBackPressed() {
        // 比如弹出一个确认对话框，或者直接退出游戏
        //某些安卓版本会生效
        // AlertHelper.show("android 点击返回按钮", () => {
              
        // })

        // 显示一个确认退出对话框（简单的实现方式，可以自行优化 UI）
        // if (confirm("你确定要退出游戏吗？")) {
        //     // 退出应用
        //     cc.game.end();
        // }
    }
    onEnterBackground(){
        if (SceneManager.instance.isGameScene && this.bundleName !== "rummy"){
            if(this.netService){
                this.netService.close()
            }
        }
    }
    onEnterForgeground(){
        if (SceneManager.instance.isGameScene && this.bundleName !== "rummy"){
            WaitHelper.show(TextTips.WAIT_ENTER_GAME_ROOM_TIPS);
            if(this.netService){
                setTimeout(() => {
                    this.netService.connect()
                }, 150);
               
            }
        }

       
    }
    onDestroy() {
        if (this.noticeInterVal) {
            clearInterval(this.noticeInterVal);
            this.noticeInterVal = null
        }
        if (this.isMiniGame) {
            GameManager.miniInstance.clearGameData();
        }
        else {
            GameManager.instance.clearGameData();
        }
        this.exit();
        cc.game.off(cc.game.EVENT_HIDE, this.onEnterBackground, this);
        cc.game.off(cc.game.EVENT_SHOW, this.onEnterForgeground, this);
    }

    //////////////////////////////////////////////////////////////////////////////
    // 获取当前游戏房间玩家个数
    public getPlayerCount(): number {
        return DataManager.instance.getRoomPlayerCount(this.roomInfo.gameid, this.roomInfo.orderid);
    }

    // 获取本地座位号
    public getLocalSeatId(seatId: number): number {
        let localSeatId = seatId - this.mySeatId;
	    if (localSeatId < 0) {
		    localSeatId = localSeatId + this.maxPlayers;
        }
	    return localSeatId;
    }


    // 是否为断线重连
    public isReconnect(): boolean {
        if (this.loginInfo && this.loginInfo.offline == 1) {
            return true;
        }
        return false;
    }

    // 是否能继续游戏
    public isContinued(): boolean {
        let money = DataManager.instance.money;
        if (this.roomInfo && money >= this.roomInfo.money) {
            return true;
        }
        return false;
    }

    // 发送数据协议
    public sendCommand(mainId:number, subId:number, data?:any) {
        if (this.netService) {
            this.netService.send(mainId, subId, data);
        }
    }

    // 发送数据协议
    public sendGameMessage(subId:number, data?:any) {
        if (this.netService) {
            cc.log("发送的协议===", MainProto.XC, subId, data)
            this.netService.send(MainProto.XC, subId, data);
        }
    }

    // 绑定协议回调接口
    public bindGameMessage(subId: number, callback: Function, target?: any) {
        if (this.netService) {
            this.netService.bindProto(MainProto.XC, subId, callback, target);
        }
    }

    // 取消绑定协议
    public unbindGameMessage(subId: number, callback: Function, target?: any) {
        if (this.netService) {
            this.netService.unbindProto(MainProto.XC, subId, callback, target);
        }
    }

    // 发送退出协议
    public sendQuitMessage() {
        this.sendCommand(MainProto.Game, Game.CS_QUIT_P);
    }
    
    // 发送匹配协议
    public sendMatchMessage() {
        this.sendCommand(MainProto.Game, Game.CS_MODE1_ENTER_PIPEI_P);
    }

    // 发送换桌子协议
    public sendHuanZhuoMessage() {
        this.sendCommand(MainProto.Game, Game.CS_HUANZHUO_P);
    }

    // 发送暂离返回协议
    public sendZanLiCombackMessage() {
        this.sendCommand(MainProto.Game, Game.CS_ROOM_ZANLI_COMBACK_P);
    }

    // 发送聊天协议
    public sendChatMessage(data:any) {
        this.sendCommand(MainProto.Game, Game.CS_ROOM_CHAT_P,data);
    }

    // 发送打赏荷官
    public sendTipDealer(data:any) {
        this.sendCommand(MainProto.Game, Game.CS_ROOM_TIPDEALER_P,data);
    }

    // 发送准备消息
    public sendGameReady(data:any){

        this.sendCommand(MainProto.Game, Game.CS_ROOM_PLAYER_READY);
        cc.log("点击继续游戏发送4-1102")
    }

    // 主动退出游戏并返回大厅
    public quitGame(info?) {
        if (this.isGaming) {
            // 参与过游戏时不能退出
            AlertHelper.show(TextTips.ALERT_EXIT_GAME_BY_GAMING);
            return ;
        }
        UIHelper.clearAll();
        this.sendQuitMessage();

        if (this.isMiniGame) {
            GameManager.miniInstance.exitGame();
        }
        else {
            GameManager.instance.exitGame(info);
        }
    }

    public quitGameResult(result){

    }

    // 打开提示提现通知
    public openWithdrawCenter() {
        let withDrawInfo = {
            tipsIndex : 0
        }
        let index = -1
        let userMoney = DataManager.instance.money
        if (DataManager.instance.ischarge === 0){
        //     userMoney = Math.ceil(userMoney/RECHARGE_USER_RATE)
        // }else {
            userMoney = Math.ceil(userMoney/NO_RECHARGE_USER_RATE)
        }
        if (userMoney >= 300 && userMoney < 400){
            index = 0
        }else if (userMoney >= 400 && userMoney < 500){
            index = 1
        }else if (userMoney >= 500 && userMoney < 600){
            index = 2
        }
        withDrawInfo = {
            tipsIndex : index
        }
        if (index === -1) return 
        EventManager.instance.emit(GameEvent.HALL_POPUP_FUNCTION, "WITH_DRAW_CENTER",withDrawInfo);
    }
    // 打开充值中心
    public openPayCenter(payInfo=null) {
        EventManager.instance.emit(GameEvent.HALL_POPUP_FUNCTION, "PAY_CENTER",payInfo);
    }

    public setRecExitTime(time:number=45000){
        GameManager.instance.setRecExitTime(time);
    }
    //////////////////////////////////////////////////////////////////////////////
    // 子类重载-开始游戏
    public start() {
        // 检查对象是否有效
        if (this.netService == null) {
            return ;
        }

        // 绑定网络协议处理接口
        this.netService.bindProto(MainProto.Game, Game.SC_ROOM_PLAYER_ENTER_P, this.onPlayerEnter, this);
        this.netService.bindProto(MainProto.Game, Game.SC_ROOM_INFO_P, this.onRoomInfo, this);
        this.netService.bindProto(MainProto.Game, Game.SC_ROOM_SET_STATE_P, this.onRoomState, this);
        this.netService.bindProto(MainProto.Game, Game.SC_ROOM_SET_PLAYER_STATE_P, this.onPlayerState, this);
        this.netService.bindProto(MainProto.Game, Game.SC_ROOM_PLAYER_QUIT_P, this.onPlayerQuit, this);
        this.netService.bindProto(MainProto.Game, Game.SC_ROOM_DEL_PLAYER_P, this.onDeletePlayer, this);
        this.netService.bindProto(MainProto.XC, XC.XC_ROOM_INFO_P, this.onToOtherRoom, this);
        this.netService.bindProto(MainProto.Game, Game.SC_ROOM_RESET_COIN_P, this.updatePlayerMoney, this);
        this.netService.bindProto(MainProto.Game, Game.SC_MODE1_ENTER_PIPEI_P, this.onStartMatch, this);
        this.netService.bindProto(MainProto.Game, Game.SC_MODE1_PIPEI_OVER_P, this.onFinishMatch, this);
        this.netService.bindProto(MainProto.XC, XC.XC_JIESUAN_P, this.onSettlement, this);
        this.netService.bindProto(MainProto.Game, Game.SC_ROOM_ZANLI_SUCCESS_P, this.onPlayerZanLi, this);
        this.netService.bindProto(MainProto.Game, Game.SC_ROOM_ZANLI_COMBACK_SUCCESS_P, this.onZanLiComback, this);
        this.netService.bindProto(MainProto.Game, Game.SC_ROOM_DEL_P, this.onQuitGame, this);
        this.netService.bindProto(MainProto.Game, Game.SC_ROOM_CHAT_P, this.onRoomChat, this);
        this.netService.bindProto(MainProto.Game, Game.SC_ROOM_TIPDEALER_P, this.onRoomTipDealer, this);
        this.netService.bindProto(MainProto.XC, Game.SC_SLOTCAT_POPUP_P, this.openWithdrawCenter, this);
        this.netService.bindProto(MainProto.XC, Game.SC_SLOTNIU_POPUP_P, this.openWithdrawCenter, this);
        this.netService.bindProto(MainProto.XC, Game.SC_SLOTS_POPUP_P, this.openWithdrawCenter, this);
        this.netService.bindProto(MainProto.XC, Game.SC_TEENPATTI_POPUP_P, this.openWithdrawCenter, this);
        
        // 注册通知回调
        this.netService.bindProto(MainProto.NoticeManager, NoticeManager.SC_NOTICE_P, this.onNotice, this);
        cc.log("进入子游戏 发送匹配协议")
        // 发送匹配协议
        this.sendMatchMessage();
    }

    // 子类重载-退出游戏
    public exit() {
        // 检查对象是否有效
        if (this.netService == null) {
            return ;
        }

        // 解除协议绑定
        this.netService.unbindProto(MainProto.Game, Game.SC_ROOM_PLAYER_ENTER_P, this.onPlayerEnter, this);
        this.netService.unbindProto(MainProto.Game, Game.SC_ROOM_INFO_P, this.onRoomInfo, this);
        this.netService.unbindProto(MainProto.Game, Game.SC_ROOM_SET_STATE_P, this.onRoomState, this);
        this.netService.unbindProto(MainProto.Game, Game.SC_ROOM_SET_PLAYER_STATE_P, this.onPlayerState, this);
        this.netService.unbindProto(MainProto.Game, Game.SC_ROOM_PLAYER_QUIT_P, this.onPlayerQuit, this);
        this.netService.unbindProto(MainProto.Game, Game.SC_ROOM_DEL_PLAYER_P, this.onDeletePlayer, this);
        this.netService.unbindProto(MainProto.XC, XC.XC_ROOM_INFO_P, this.onToOtherRoom, this);
        this.netService.unbindProto(MainProto.Game, Game.SC_ROOM_RESET_COIN_P, this.updatePlayerMoney, this);
        this.netService.unbindProto(MainProto.Game, Game.SC_MODE1_ENTER_PIPEI_P, this.onStartMatch, this);
        this.netService.unbindProto(MainProto.Game, Game.SC_MODE1_PIPEI_OVER_P, this.onFinishMatch, this);
        this.netService.unbindProto(MainProto.XC, XC.XC_JIESUAN_P, this.onSettlement, this);
        this.netService.unbindProto(MainProto.Game, Game.SC_ROOM_ZANLI_SUCCESS_P, this.onPlayerZanLi, this);
        this.netService.unbindProto(MainProto.Game, Game.SC_ROOM_ZANLI_COMBACK_SUCCESS_P, this.onZanLiComback, this);
        this.netService.unbindProto(MainProto.Game, Game.SC_ROOM_DEL_P, this.onQuitGame, this);
        this.netService.unbindProto(MainProto.Game, Game.SC_ROOM_CHAT_P, this.onRoomChat, this);
        this.netService.unbindProto(MainProto.NoticeManager, NoticeManager.SC_NOTICE_P, this.onNotice, this);
        this.netService.unbindProto(MainProto.Game, Game.SC_ROOM_TIPDEALER_P, this.onRoomTipDealer, this);
        this.netService.unbindProto(MainProto.XC, Game.SC_SLOTCAT_POPUP_P, this.openWithdrawCenter, this);
        this.netService.unbindProto(MainProto.XC, Game.SC_SLOTNIU_POPUP_P, this.openWithdrawCenter, this);
        this.netService.unbindProto(MainProto.XC, Game.SC_SLOTS_POPUP_P, this.openWithdrawCenter, this);
        this.netService.unbindProto(MainProto.XC, Game.SC_TEENPATTI_POPUP_P, this.openWithdrawCenter, this);
    }

    // 子类重载-开始匹配
    public onStartMatch(info: any) {
        //Common.dump(info, "onStartMatch");
    }
    
    // 子类重载-匹配成功
    public onFinishMatch(info: any) {
        //Common.dump(info,"onFinishMatch");
    }
    
    // 子类重载-玩家加入
    public onPlayerEnter(info: any) {
        //Common.dump(info,"onPlayerEnter");
    }
    
    // 子类重载-玩家离开
    public onPlayerQuit(info: any) {
        //Common.dump(info,"onPlayerQuit");
    }
    
    // 子类重载-游戏结束,你条件不满足被踢出房间,如果你在暂离状态,也会被踢出房间
    public onDeletePlayer(info: any) {
        //Common.dump(info, "onDeletePlayer");
    }
    
    // 子类重载-玩家状态
    public onPlayerState(info: any) {
        //Common.dump(info,"onPlayerState");
    }
    
    // 子类重载-更新玩家金币
    public updatePlayerMoney(info: any) {
        if (info.playerid == DataManager.instance.playerid) {
            DataManager.instance.money = info.coin
            EventManager.instance.emit(GameEvent.MONEY_CHANGE_NOTICE, info.coin);
            SceneManager.instance.showHallViewHelpMoney()
        }
        //Common.dump(info, "updatePlayerMoney");
     }
    
    // 子类重载-进入房间，房间信息
    public onRoomInfo(info: any) {
        //Common.dump(info, "onRoomInfo");
        cc.log("meizoum")
        this.roomState = info.roomstate;
        this.singlechatfee = info.singlechatfee;
        this.tipdealerfee = info.tipdealerfee;
        
        let playerlist = info["playerlist"];
        if (playerlist && typeof (playerlist) == "object") {
            // 先找出自己的位置
            for (let key in playerlist) {
                let plyInfo = playerlist[key];
                let playerid = Common.toInt(plyInfo["playerid"]);
                if (playerid == this.playerid) {
                    this.mySeatId = Common.toInt(plyInfo["seat"]);
                    break;
                }
            }
        }
    }
    
    // 子类重载-房间状态
    public onRoomState(info: any) {
        this.roomState = info.roomstate;
        //Common.dump(info,"onRoomState");
    }
    
    // 子类重载-房间信息
    public onToOtherRoom(info: any) {
        //Common.dump(info, "onToOtherRoom");
    }
    
    // 子类重载-结算
    public onSettlement(info: any) {
        //Common.dump(info, "onSettlement");
    }

    // 子类重载-玩家暂离
    public onPlayerZanLi(info: any) {
        //Common.dump(info, "onPlayerZanLi");
    }

    // 子类重载-玩家暂离返回成功
    public onZanLiComback(info: any) {
        //Common.dump(info, "onPlayerZanLi");
    }

    // 子类重载-退出游戏
    public onQuitGame(info: any) {
        if (SceneManager.instance.isGameScene) {
            SceneManager.instance.enterHallScene();
        }
    }
    // 响应-通知 隐藏底分信息
    private onNotice(result:any) {
        SceneManager.instance.showNotice(result);
    }

    public onRoomTipDealer(info){

    }
    //聊天消息
    public onRoomChat(info:any){

    }

    //////////////////////////////////////////////////////////////////////////////

}
