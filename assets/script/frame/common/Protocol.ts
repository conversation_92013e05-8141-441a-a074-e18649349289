//////////////////////////////////////////////////////////////////////////////////
// 主协议
export let MainProto =  {
    RegLogin: 0,                    //注册登录
    FindPsw: 1,                     //找回密码
    Pay: 2,                         //支付相关
    IDRecord: 3,                    //查询ID记录系统
    Game: 4,                        //游戏逻辑
    XC: 5,                          //子游戏服务器和客户端交互的协议

    BaseInfo: 6,                    //基本信息
    Money: 7,                       //金钱钱包相关
    LevelExp: 8,                    //等级经验相关
    EMail: 9,                       //邮箱
    Phone: 10,                      //手机号
    Cryptoguard: 11,                //密保相关
    GameTime: 12,                   //游戏时间
    MoneyRecord: 13,                //金钱钱包记录
    MailManager: 14,                //邮件系统
    NoticeManager: 15,              //公告系统
    CompetitionManager: 16,         //大奖赛
    TopPlayerManager: 17,           //大奖赛置顶玩家
    Relief: 18,                     //救济金
    LoginRecord: 19,                //登陆记录
    OnlineReward: 20,               //在线奖励
    DayLogin: 21,                   //每日签到
    Lock: 22,                       //锁机
    CheckSystem: 23,                //验证系统
    ChengJiuManager: 24,            //成就
    TaskManager: 25,                //任务
    Present: 26,                    //礼品
    PresentManager: 27,             //礼品管理
    GameRecord: 28,                 //游戏记录
    PresentNotice: 29,              //礼品领取公告
    Vip: 30,                        //vip
    PlayerTimer: 31,                //玩家定时器
    Card: 32,                       //实物卡
    AccountInfo: 33,                //账号信息

    DBServer: 34,                   //HallServer,GameServer,FCServer和DBServer通信
    ChatServer: 35,                 //聊天服务器

    LocalServer: 36,                //本地存储
    Rank: 40,                       //排行榜
    QMAgent: 41,                    //全民代理   (弃用  主协议改为 DBServer: 34)
    Task: 42,                       //活动任务

    /**----------------------新的协议 100 开始------------------------- */
    HallActivity:101,        //大厅活动相关协议
}

// 子协议 - 登陆相关
export let RegLogin = {
    CS_NORMAL_REG_P: 0,             //普通注册
    SC_NORMAL_REG_P: 1,
    CS_LOGIN_P: 2,                  //开始登陆
    SC_LOGIN_P: 3,
    CS_LOGIN_OUT_P: 4,              //登出
    SC_ONLINE_P: 5,                 //发送上线信息

    CS_CHECK_REGINFO_P: 6,          //校验注册信息
    SC_CHECK_REGINFO_P: 7,          //["code"]:emNoramalReg
    CS_GET_RANDOM_NICKNAME_P: 8,    //客户端请求一个随机昵称
    SC_GET_RANDOM_NICKNAME_P: 9,
    SC_OHTER_LOGIN_P: 10,           //你的账号在别处登录,你已经被挤下线
    SC_LOGIN_OTHER_P: 11,           //你的账号在别处登录,你把它挤下线

    SC_SERVER_STOP_P: 12,           //服务器处于停机维护状态

    SC_UPDATE_SAVE_RANDOM_P: 13,    //更新保存密码随机数
    SC_FULLCONNECT_ATTACK_P: 14,    //因为全连接攻击,你被断开连接
    SC_WEB_KILL_P: 15,              //你被踢下线

    //GameServer
    CS_GAMESERVER_LOGIN_P: 16,
    SC_GAMESERVER_LOGIN_P: 17,

    SC_GAMESERVER_ONLIEN_P: 18,

    CS_HEART_CHECK_P: 19,
    SC_HALL_SERVER_VERSION_P: 20,   //大厅版本号
    SC_GAME_SERVER_VERSION_P: 21,   //游戏服务版本号

    CS_A_LOGIN_P: 22,
    SC_A_LOGIN_P: 23,

    CS_B_LOGIN_PC_P: 24,
    CS_B_LOGIN_PHONE_P: 25,
    SD_B_LOGIN_P: 26,               //发到DB玩家上线
    SC_B_LOGIN_P: 27,
    CS_WINDOW_MIN_WAIT_P: 28,       //进入最小化等待
    CS_REQUEST_REG_PHONECODE_P: 29, //手机注册请求手机验证码
    SC_REQUEST_REG_PHONECODE_P: 30,

    CS_PHONECODE_REG_P: 31,         //手机注册
    SC_PHONECODE_REG_P: 32,


    CS_C_LOGIN_P: 33,
    SC_C_LOGIN_P: 34,
    CS_CHECK_SAFE_CLOSE_SERVER_P: 35,//验证后关服
    CS_CHECK_CONNECT_P: 36,         //检测是否与服务器消息发送是否正常
    SC_CHECK_CONNECT_P: 37,

    CS_REQUEST_SERVER_VERSION_P: 38,//请求版本号

    CS_REQUEST_VERCODE_P: 41,       //请求
    SC_REQUEST_VERCODE_P: 42, 
    CS_RESPONSE_VERCODE_P: 43,
    SC_VERCODE_HALL_RESULT_P: 44,
    SC_VERCODE_GAME_RESULT_P: 45,

    //以下是新协议
    CD_REQUEST_SYSTEM_STATUS_P: 49, //请求系统配置
    DC_REQUEST_SYSTEM_STATUS_P: 50, //返回系统配置
    CS_REQUEST_GAMEVERSIONS_P: 51,  //请求游戏版本号列表
    SC_REQUEST_GAMEVERSIONS_P: 52,  //服务器下发游戏版本号列表
}

// 子协议 - 基础信息
export let BaseInfo = {
    CS_SET_NICKNAME_P: 0,           //设置昵称
    SC_SET_NICKNAME_RESULT_P: 1,
    SC_SET_NICKNAME_P: 2,

    CS_SET_HEADID_P: 3,             //设置头像ID
    CS_SET_CUSTOM_HEAD_P: 4,        //设置为使用自定义头像

    CS_CHANGE_PSW_P: 5,             //修改密码
    SC_CHANGE_PSW_RESULT_P: 6,

    SC_SET_LOTTERY_P: 7,            //奖券改变

    CS_CHANGE_PSW_CHECK_P: 8,       //验证修改后密码有效性
    SC_CHANGE_PSW_CHECK_P: 9,

    CS_SET_SPECPHONE_P: 10,         //设置特殊手机号
    SC_SET_SPECPHONE_P: 11,

    CS_FRIEND_P: 12,                //朋友圈
    SC_FRIEND_P: 13,

    SC_SET_ROBOT_LEVEL_P: 14,

    SC_CHANGE_LOTTERY_P: 15,        //变更奖券(变更量)

    CD_SET_SEX_P: 16,               //设置玩家的性别
    DC_SET_SEX_P: 17,
}

//DbServer
export let DbServer = {
    SC_SET_HEADID_P:43,             //设置头像返回
    SC_WEB_CHANGE_ATTRIB_P:37,      //web请求变更玩家的属性

    CS_CUSTSRV_REPLY_P : 110,       //获取客服消息数据
    SC_CUSTSRV_REPLY_P : 111,       //响应客服消息数据

    CS_NEW_CHARGE_LIST_P : 112,       //获取未读充值消息
    SC_NEW_CHARGE_LIST_P : 113,       //获取未读充值消息

    CS_CUSTSRV_REDAY_MESSAGE : 114,       //发送已读客服信息

    CD_QUERY_GIFT_PACK_P : 192,            //新的查询礼包信息
    DC_QUERY_GIFT_PACK_P : 193,            //返回查询结果
    CD_STATISTICS_CHANNEL_DATA : 194,      //FBC统计数据
}

// 子协议 - 金币
export let Money = {
    SC_SET_MONEY_P: 0,              //金钱改变
    SC_SET_WALLETMONEY_P: 1,        //钱包改变

    CS_SAVE_MONEY_P: 2,             //存钱
    SC_SAVE_MONEY_RESULT_P: 3,

    CS_GET_MONEY_P: 4,              //取钱
    SC_GET_MONEY_RESULT_P: 5,

    CS_TEST_ADD_MONEY_P: 6,         //测试,加钱
    SC_SET_GAME_MONEY_P: 7,         //金钱改变
    SC_SET_GAME_WALLETMONEY_P: 8,   //钱包改变

    SC_SET_HONOR_VALUE_P: 9,        //荣誉点改变
    SC_SET_DIAMONDS_VALUE_P: 10,    //钻石改变

    CS_DIAMONDS_CHANGE_MONEY_P: 11, //钻石 兑换金币
    SC_DIAMONDS_CHANGE_MONEY_P: 12,

    CS_DIAMONDS_CHANGE_VIP_P: 13,   //钻石 兑换会员
    SC_DIAMONDS_CHANGE_VIP_P: 14,

    CS_DIAMONDS_TRANS_MONEY_CONFING_P: 15,//钻石兑换金币配置
    SC_DIAMONDS_TRANS_MONEY_CONFING_P: 16,

    CS_DIAMONDS_TRANS_VIP_CONFING_P: 17,//钻石兑换会员配置
    SC_DIAMONDS_TRANS_VIP_CONFING_P: 18,


    CS_RMB_TRANS_DIAMONDS_CONFING_P: 19,//人民币换钻石配置
    SC_RMB_TRANS_DIAMONDS_CONFING_P: 20,

    CS_RMB_TRANS_DIAMONDS_CONFING_IOS_P: 21,//人民币换钻石配置_IOS
    SC_RMB_TRANS_DIAMONDS_CONFING_IOS_P: 22,

    CS_TRANSFER_MONEY_P: 23,        //玩家转帐
    SC_TRANSFER_MONEY_P: 24,

    CD_BIND_PICKUP_P: 25,           //绑定提取号(支付宝\微信\卡)
    DS_BIND_PICKUP_P: 26,

    CD_BANK_PASSWORD_P: 27,         //修改银行密码
    DS_BANK_PASSWORD_P: 28,

    CD_MONEY_CHANG_RMB_P: 29,       //游戏币兑换现金
    DS_MONEY_CHANG_RMB_P: 30,

    CD_SEND_MSG_GUEST_SERVER_P: 31, //发送消息给客服务
    DC_SEND_MSG_GUEST_SERVER_P: 32,
    CD_BIND_BANK_P: 33,             // 绑定银行卡
    DS_BIND_BANK_P: 34,             // 绑定银行卡返回                  

    // 返还金
    CS_REQUEST_ALMS_P: 37,          //查询救济金
    SC_REQUEST_ALMS_RESULT_P: 38,   //查询救济金结果 
    CS_GET_ALMS_P: 35,              //领取救济金 
    SC_GET_ALMS_RESULT_P: 36,       //领取救济金结果

    // 服务器定义
    CS_AGENT_COMPLAINT_P: 46,       //投诉代理
    CS_SERVICE_COMPLAINT_P: 47,     //投诉客服
    SC_COMPLAINT_RESULT_P: 48,      //投诉结果
    CS_PAY: 49,                     //请求支付地址
    SC_PAY: 50,                     //请求支付返回
    CS_VIP_PAY_LIST_P: 51,          //请求VIP支付列表
    SC_VIP_PAY_LIST_P: 52,          //请求VIP支付列表返回
}

// 子协议 - 密码
export let FindPsw = {
    CS_FINDPSW_P: 0,                //找回密码
    SC_FINDPSW_P: 1,

    CS_FINDPSW_REQUEST_CODE_P: 2,   //请求手机验证码
    SC_FINDPSW_REQUEST_CODE_RESULT_P: 3,

    CS_FINDPSW_CRYPT_P: 4,          //输入密保答案
    CS_FINDPSW_PHONECODE_P: 5,      //输入手机验证码答案
    SC_FINDPSW_CKECK_P: 6,          //验证结果

    CS_FINDPSW_SET_NEW_PSW_P: 7,    //验证结束,设置新密码
    SC_FINDPSW_SET_NEW_PSW_RESULT_P: 8
}

// 子协议 - 通知管理
export let NoticeManager = {
    SC_NOTICE_P: 0,
    CS_SEND_NOTICE_P: 1,            //请求发送公告
    SC_SEND_NOTICE_P: 2,
    CS_REQUEST_NOTICE_NEED_P: 3,    //请求发送公告所需
    SC_REQUEST_NOTICE_NEED_P: 4,

    CD_REQUEST_SYSTEM_NOTICE_P: 5,  //请求系统公告内容
    DC_REQUEST_SYSTEM_NOTICE_P: 6
}

// 子协议 - 排行榜
export let Rank = {
    CS_RANK_DATA: 0,                //获得排行榜信息
    SC_RANK_DATA: 1,                //后端返回排行榜信息

    CD_RANK_LIST: 2,                //向dbserver获得排行榜信息
    DC_RANK_LIST: 3,                //dbserver返回排行榜信息

    CS_SELF_RANK_DATA_P: 4,         //自己的 今日金币排行榜 {type: type} 需要加一个类型
    SC_SELF_RANK_DATA_P: 5,

    CD_SELF_RANK_DATA: 6,
    DC_SELF_RANK_DATA: 7,
}

// 子协议 - 邮件管理
export let MailManager =  {

    CS_REQUEST_MAIL_INFO_P: 0,      //请求一封邮件的内容
    SC_REQUEST_MAIL_INFO_P: 1,

    CS_MAIL_SET_READ_P: 2,          //请求将一封邮件设置为已读
    CS_DEL_MAIL_INFO_P: 3,          //请求删除一封邮件

    SC_ADD_MAIL_P: 4,               //添加一封邮件

    CS_REQUEST_MAILLIST_P: 5,       //请求邮件列表
    SC_REQUEST_MAILLIST_P: 6,

    CS_REQUEST_NEW_MAIL_COUNT_P: 7,
    SC_REQUEST_NEW_MAIL_COUNT_P: 8,
}

// 子协议 - 游戏
export let Game = {
    SC_ADD_GAMELIST_P: 0,
    SC_DEL_GAMELIST_P: 1,

    //房间协议
    SC_ROOM_INFO_P: 2,              //房间数据
    CS_ROOM_SET_PLAYER_STATE_P: 3,  //玩家设置自己的状态
    SC_ROOM_SET_PLAYER_STATE_P: 4,  //设置玩家状态(广播)
    SC_ROOM_SET_STATE_P: 5,         //设置房间状态(广播)
    CS_ROOM_CHAT_P: 6,              //聊天
    SC_ROOM_CHAT_P: 7,              //聊天(广播)
    SC_ROOM_RESET_COIN_P: 8,

    SC_ROOM_ZANLI_SUCCESS_P: 9,     //暂离成功
    CS_ROOM_ZANLI_COMBACK_P: 10,    //玩家请求暂离回来
    SC_ROOM_ZANLI_COMBACK_SUCCESS_P: 11,//玩家请求暂离回来成功

    SC_ROOM_PLAYER_ENTER_P: 12,     //玩家进入房间(广播)
    SC_ROOM_WATCH_ENTER_P: 13,      //观看者进入房间(广播)
    SC_ROOM_PLAYER_QUIT_P: 14,      //玩家离开房间(广播)
    SC_ROOM_WATCH_QUIT_P: 15,       //观看者离开房间(广播)
    SC_ROOM_DEL_P: 16,              //同意退出房间

    SC_ROOM_PREPARE_TIMEOUT_P: 17,  //准备超时,你被踢出房间
    SC_ROOM_DEL_PLAYER_P: 18,       //游戏结束,你条件不满足被踢出房间,如果你在暂离状态,也会被踢出房间
    SC_ROOM_DEL_WATCH_P: 19,        //你是观看者,由于房间已经没有人了,你被踢出房间

    //子游戏服务器协议
    XS_REGISTER_P: 20,              //子游戏服务器注册
    XS_DEL_WATCH_P: 21,             //删除观看者
    XS_PLAYER_ZANLI_P: 22,          //玩家暂离
    XS_PLAYER_RESULT_P: 23,         //一个玩家结算
    XS_RESULT_P: 24,                //游戏结束结算
    XS_RESET_COIN_P: 25,

    SX_CREATE_GAME_P: 26,           //开始游戏 
    SX_ADD_WATCH_P: 27,             //添加观看者
    SX_DEL_WATCH_P: 28,             //删除观看者
    SX_RESET_COIN_P: 29,
    SX_PLAYER_LEAVE_P: 30,          //玩家离线托管
    SX_PLAYER_ONLINE_P: 31,         //玩家离线托管后上线
    SX_PLAYER_ZANLI_COMBACK_P: 32,  //玩家暂离回来
    SX_QUIT_P: 33,                  //客户端点击X

    //公共
    CS_GAME_PLAYER_NUM_P: 34,       //请求每个游戏玩家人数表
    SC_GAME_PLAYER_NUM_P: 35,
    SC_UPDATE_GAME_LIST_P: 36,      //更新游戏列表
    CS_SELECT_GAME_P: 37,           //客户端选择一个游戏
    SC_SELECT_GAME_P: 38,
    CS_ROBOT_ADD_MONEY_P: 39,       //机器人请求加钱
    CS_QUIT_P: 40,
    CS_WATCH_P: 41,                 //请求观看
    SC_WATCH_P: 42,
    CS_HUANZHUO_P: 43,              //换桌
    SC_HUANZHUO_P: 44,

    //模式1
    CS_MODE1_ENTER_P: 45,           //玩家请求进入
    CS_MODE1_ROBOT_ENTER_P: 46,     //机器人请求进入
    SC_MODE1_ENTER_P: 47,
    SC_MODE1_ROBOT_FAILD_P: 48,     //机器人请求进入后续失败

    //模式2
    CS_MODE2_ENTER_P: 49,           //玩家请求进入模式2
    SC_MODE2_DATA_P: 50,            //子厅可见数据
    SC_MODE2_ADD_PLAYER_P: 51,      //子厅可见数据,增加一个玩家
    SC_MODE2_DEL_PLAYER_P: 52,      //子厅可见数据,删除一个玩家
    SC_MODE2_ROOM_STATE_P: 53,      //子厅可见数据,房间状态改变
    SC_MODE2_DATA_CREAT_ROOM_P: 54, //子厅可见数据,增加一张桌子
    SC_MODE2_DATA_CLEAR_ROOM_P: 55, //子厅可见数据,清空一张桌子

    CS_MODE2_CREATE_ROOM_P: 56,     //创建房间
    SC_MODE2_CREATE_ROOM_P: 57,     //结果
    CS_MODE2_ENTER_ROOM_P: 58,      //进入房间
    SC_MODE2_ENTER_ROOM_P: 59,      //结果

    //模式3
    CS_MODE3_ENTER_P: 60,           //玩家请求进入模式3 

    CS_MODE3_COMPDETAIL_EXIT_P: 61, //玩家离开大奖赛详情界面-不离开MOD3模式（请求离开模式3或退出游戏才退出MODE3模式
    CS_MODE3_GAME_ENTER_P: 62,      //玩家请求进入模式3某个大奖赛进行游戏CS_MODE3GAME_ENTER_P 

    SC_MODE3_ENTER_P: 63,           //玩家进入模式3

    SC_MODE3_GAME_ENTER_P: 64,      //玩家进入模式3中一个大奖赛进行游戏SC_MODE3GAME_ENTER_P
    SC_MODE3_RETURNERROR_P: 65,     //返回错误信息协议

    CS_MODE3_ENTER_PIPEI_P: 66,     //一局完成后,通知其继续匹配
    SC_MODE3_ENTER_PIPEI_P: 67,     //通知其进入匹配状态
    SC_MODE3_PIPEI_OVER_P: 68,      //匹配成功,通知进入房间
    SC_MODE3_QUIT_PIPEI_SUCCESS_P: 69,//退出匹配成功(模式3退出时发送给客户端，没有这个协议，玩家打完一局退出时，就不会关闭游戏窗口)

    CS_MODE3_CHAT_P: 70,            //玩家发送聊天信息
    SC_MODE3_CHAT_P: 71,            //玩家发送聊天信息(服务器接收到玩家聊天信息之后，要广播)

    SC_MODE3_PALYERONLINE_P: 72,    //玩家上下线协议

    CS_MODE3_GETCOMPPLAYERRANK_P: 73,//获得大奖赛玩家排名
    SC_MODE3_GETCOMPPLAYERRANK_P: 74,//下发大奖赛玩家排名

    SC_MODE3_OTHER_ONLINE_P: 75,    //其它在线玩家


    SC_GAME_CLOSE_P: 76,            //此游戏被关闭
    SX_KILL_P: 77,                  //要求子游戏服务器关闭
    SX_ADD_PLAYER_P: 78,            //可以中途进入的游戏,增加新玩家
    XS_ADD_PLAYER_P: 79,
    SX_JIESUAN_ALL_PLAYER_P: 80,    //优雅关服,要求子游戏结算所有玩家
    SX_WEB_SET_P: 81,               //网站设置数据
    SX_WEB_GET_P: 82,               //网站获取数据
    XS_WEB_GET_P: 83,
    XS_VIRTUAL_ENDGAME_P: 84,       //虚拟结束游戏
    SC_VIRTUAL_ENDGAME_P: 85,

    SC_ROOM_DELETE_P: 86,           //房间被主动删除,通知客户端
    XS_PLAYER_HAND_TUO_GUAN_P: 87,  //玩家进入手动托管

    XS_ADD_WATCH_FAIL_P: 88,        //增加观察者失败
    XS_XIAOBAISHA_24_P: 89,         //一个玩家在小白鲨压中24倍

    CS_REQUEST_UNONLINE_CLEW_P: 90, //请求得到离线挽留信息
    SC_REQUEST_UNONLINE_CLEW_P: 91,

    SC_ROBOT_START_PREPARE_P: 92,   //通知子服务器让玩家准备

    CS_MODE1_ENTER_PIPEI_P: 93,     //一局完成后,通知其继续匹配
    SC_MODE1_ENTER_PIPEI_P: 94,     //通知其进入匹配状态
    SC_MODE1_PIPEI_OVER_P: 95,      //匹配成功,通知进入房间
    SC_MODE1_QUIT_PIPEI_SUCCESS_P: 96,//退出匹配成功
    XS_WEB_SET_P: 97,               // web设置后，返回给web数据协议
    SC_CHANGE_PLAYER_GAME_RESULT_P: 98,//改变玩家游戏结果
    SC_MODE3_PIPEI_STOP_P: 99,      //停止比赛匹配

    CS_FORCE_CLOSE_SOCKET_P: 100,   //强制与服务器断开

    XS_SAFE_CREATE_ROOM_P: 101,     //安全创建房间

    SX_RELOAD_CONFIG_P: 102,        //重新加载游戏配置

    XS_VIRTUAL_ENDGAME_OVER_P: 103, //虚拟结算完成通知GameServer对桌面成员进行处理
    XS_RESULT_OVER_P: 104,          //结算完成通知GameServer对桌面进行处理 

    SX_UPDATE_LOGKEY_P: 105,        //更新日志 
    SX_UPDATE_PLAYER_INFO_P: 106,   //更新玩家信息到子游戏对100人游戏
    SX_DEL_ROOM_P: 107,             //删除指定的房间
    SC_PLAYER_LEAVE_MODE_P: 108,    //离开模式
    CS_REQUEST_ROOM_P: 109,         //重新请求一次游戏房间数据
    SC_REQUEST_ROOM_P: 110,

    CS_REQUEST_BROADCAST_PLAYERNUM_P: 111,//请求指定游戏主播房间人数
    SC_REQUEST_BROADCAST_PLAYERNUM_P: 112,

    SC_ADD_BROADCAST_GAME_P: 113,   //增加主播游戏
    SC_DEL_BROADCAST_GAME_P: 114,   //减少主播游戏

    SC_RAND_ROOM_CHAT_P: 115,       //随机在真人房间内说句话
    SC_ROOM_VIP_DEL_P: 116,         //Vip退出删除房间

    CS_BROAD_PLAYERNUM_DETAIL_P: 117,//请求指定游戏类别的主播玩家数量统计
    SC_BROAD_PLAYERNUM_DETAIL_P: 118,

    SC_UPDATE_GAME_P: 119,          //变更游戏服务器参数(低分、显示名字变更) 

    SC_SET_PLAYER_SEATID_P: 120,    //设置玩家的坐位ID,暂只是robot有用
    SC_CLEAR_PLAYER_SEATID_P: 121,  //清除于家的坐位ID,暂只用于robot

    XS_PLAYER_ONLINE_FAIL_P: 122,   //请求得到子游戏数据失败

    CS_REQUEST_ENTER_GAME_P: 123,   // 客户端请求进入游戏房间
    SC_REQUEST_ENTER_GAME_P: 124,   // 客户端请求进入游戏房间返回

    CS_PLAYER_REPORT_P: 125,        // 玩家举报
    SC_PLAYER_REPORT_P: 126,        // 举报的响应
    SC_SLIDE_VERIFY_REQUEST_P: 127, // 发送验证请求 Server -> Client
    CS_SLIDE_VERIFY_P: 128,         // 客户端验证结果 Client -> Server 
    SC_SLIDE_VERIFY_P: 129,         // 服务器验证结果 Server -> Client
    GS_2_CLIENT_LOGIN_KEY_P: 132,   //捕鱼End

    CS_ROOM_TIPDEALER_P: 158,		//打赏荷官
	SC_ROOM_TIPDEALER_P: 159,		//打赏荷官(广播)

	SX_SYNC_ROOMINFO_P: 1100,		//同步游戏房间信息
	XS_SYNC_ROOMINFO_P: 1101,		//同步游戏房间信息
    CS_ROOM_PLAYER_READY: 1102,     //游戏准备消息（Rummy 特殊情况处理）

    //百家乐游戏消息
    CS_GAME_BJL_ALLINFO_P: 1300,    // 请求百家乐全部桌面信息
    SC_GAME_BJL_ALLINFO_P: 1310,    // 返回百家乐全部桌面信息
    CS_MODE4_ENTER_P: 1320,         // 玩家请求进入
    CS_MODE4_ROBOT_ENTER_P: 1330,   // 机器人请求进入
    SC_MODE4_ENTER_P: 1340,
    SC_MODE4_ROBOT_FAILD_P: 1350,   // 机器人请求进入后续失败
    XS_UPDATE_GAMERESULT_P: 1360,   // 传局结果
    SC_SLOTCAT_POPUP_P:3000,
    SC_SLOTNIU_POPUP_P:3000,
    SC_SLOTS_POPUP_P: 3000,
    SC_TEENPATTI_POPUP_P:3000,
}

// 子协议 - XC
export let XC = {
    XC_ROOM_INFO_P: 0,              //房间数据
    XC_JIESUAN_P: 1,                //结算数据

    SC_VIRTUAL_BROADCAST_TIPS_P: 2, //虚拟主播游戏进行时的TIPS
    XC_BROADCAST_PROTOCOL_P: 3,     //子游戏发送的广播消息
    XC_ROBOT_BROADCAST_P: 4,        //子游戏广播给所有机器人的协议
}

// 子协议 - 全民代理
export let QMAgent = {
    CS_AGENT_PROMOTIONDATA: 0,      //获得推广佣金信息
    SC_AGENT_PROMOTIONDATA: 1,      //返回获得推广佣金信息
    CS_AGENT_GETMONEY: 2,           //领取佣金
    SC_AGENT_GETMONEY: 3,           //领取佣金
    CS_AGENT_MONEYDETAIL: 4,        //佣金明细
    SC_AGENT_MONEYDETAIL: 5,        //佣金明细
    CS_AGENT_MYTEAM: 6,             //我的团队
    SC_AGENT_MYTEAM: 7,             //我的团队

    CD_GET_AGENT_CONFIG_P: 3000,    //获取代理配置
    DC_GET_AGENT_CONFIG_P: 3001,    //返回代理配置
    CD_GET_AGENT_DIRECT_TOTAL_P: 3002,  //自己的代理信息
    DC_GET_AGENT_DIRECT_TOTAL_P: 3003,  //返回代理信息
    CD_GET_AGENT_DIRECT_DETAIL_P: 3004, //自己的收益明细
    DC_GET_AGENT_DIRECT_DETAIL_P: 3005, //返回收益明细
    CD_GET_AGENT_LIST_P: 3006,  //获取下级列表
    DC_GET_AGENT_LIST_P: 3007,  //返回下级列表
    CD_GET_AGENT_RANK_P: 3008,  //代理收益排行榜
    DC_GET_AGENT_RANK_P: 3009,  //返回代理收益排行榜
    CD_GET_AGENT_EXCHANGE_P: 3010,//代理兑换
    DC_GET_AGENT_EXCHANGE_P: 3011,//返回兑换消息
    CD_BAND_AGENT_RAND_CRAD_P: 3012, //代理绑定银行卡或者修改银行卡
    DC_BAND_AGENT_RAND_CRAD_P: 3013, //返回绑定银行卡
}

// 子协议 - 任务
export let Task = {
    CS_UPDATE_TASK_LIST: 0,         //更新任务列表信息
    SC_UPDATE_TASK_LIST: 1,         //更新任务列表信息
    CS_GET_TASK_REWARD: 2,          //领取任务奖励
    SC_GET_TASK_REWARD: 3,          //领取任务奖励返回

    SC_UPDATE_MATCH_ATTR: 10,               //更新比赛状态
    CS_GET_MATCH_LIST: 11,                  //获取比赛列表信息
    SC_GET_MATCH_LIST: 12,                  //获取比赛列表信息
    CS_GET_MATCH_RANK_REWARD: 13,           //获取比赛排名和奖励
    SC_GET_MATCH_RANK_REWARD: 14,           //获取比赛排名和奖励返回
    CS_GET_TODAY_MATCH_LIST: 15,            //获取今日比赛列表信息
    SC_GET_TODAY_MATCH_LIST: 16,            //获取今日比赛列表信息
}

//子协议 - 大厅活动相关协议
export let HallActivity = {
    CS_LOGINCASH_INFO_P: 0,                 //请求登录活动信息
    SC_LOGINCASH_INFO_P: 1,                 //请求登录活动信息返回

    CS_FETCH_LOGINCASH_AWARD_P: 2,          //领取登录活动奖励 参数 fetchtype 0 登录 1 充值 2 游戏局数 3转盘 4 游戏赢分 10 完成 ip 用户ip
    SC_FETCH_LOGINCASH_AWARD_P: 3,          //领取登录活动奖励返回 参数 fetchaward 领取奖励数值

    CS_GET_USER_MONEY_P: 4,                 //获取用户金币信息
    SC_GET_USER_MONEY_P: 5,                 //返回用户金币信息

    CS_FETCH_USER_BONUS_P: 6,               //领取用户积分信息 参数 fetchamount 领取积分 ip 用户ip 
    SC_FETCH_USER_BONUS_P: 7,               //返回领取用户积分信息

    CS_GET_SEVEN_DAYS_P: 8,               //请求7日签到活动信息
    SC_GET_SEVEN_DAYS_P: 9,               //返回7日签到活动信息

    CS_FETCH_SEVEN_DAYS_AWARD_P: 10,               //领取7日签到活动奖励 参数 fetchid 领取活动id ip 用户ip
    SC_FETCH_SEVEN_DAYS_AWARD_P: 11,               //返回7日签到活动信息

    CS_GET_THIRTY_CARD_P: 12,               //请求30次卡活动信息
    SC_GET_THIRTY_CARD_P: 13,               //返回30次卡活动信息

    CS_FETCH_THIRTY_CARD_P: 14,               //领取30次卡活动奖励
    SC_FETCH_THIRTY_CARD_P: 15,               //返回30次卡活动信息

    CS_GAME_TASK_P: 16,               //请求游戏任务信息
    SC_GAME_TASK_P: 17,               //返回游戏任务信息
    CS_FETCH_GAME_TASK_AWARD_P: 18,               //领取游戏任务奖励
    SC_FETCH_GAME_TASK_AWARD_P: 19,               //返回游戏任务信息

    CS_GET_GIFT_CHARGE_P: 20,               //获取礼包活动信息
    SC_GET_GIFT_CHARGE_P: 21,               //返回礼包活动信息

    CS_GET_CARD_TASK_P: 22,               //获取周卡月卡活动信息
    SC_GET_CARD_TASK_P: 23,               //返回周卡月卡活动信息
    CS_FETCH_CARD_TASK_P: 24,               //领取周卡月卡活动奖励
    SC_FETCH_CARD_TASK_P: 25,               //返回周卡月卡活动奖励

    CS_GET_VIP_GIFT_P: 26,                 //获取VIP活动 
    SC_GET_VIP_GIFT_P: 27,                 //返回获取VIP 
    CS_FETCH_VIP_GIFT_P: 28,               //领取VIP活动奖励  参数： fetchtype 0 日奖励 1 周奖励 2 月奖励
    SC_FETCH_VIP_GIFT_P: 29,               //返回VIP活动奖励

    CS_GET_FREE_BONUS_P: 30,                 //获取免费积分 
    SC_GET_FREE_BONUS_P: 31,                 //返回免费积分 
    CS_FETCH_FREE_BONUS_P: 32,               //领取免费积分活动奖励 
    SC_FETCH_FREE_BONUS_P: 33,               //返回免费积分活动奖励

    CS_GET_FREE_CASH_P: 34,                 //获取免费提现 
    SC_GET_FREE_CASH_P: 35,                 //返回免费提现 
    CS_FETCH_FREE_CASH_P: 36,               //领取免费提现 
    SC_FETCH_FREE_CASH_P: 37,               //返回免费提现 
    CS_GET_FREE_CASH_INVITATION_P: 38,      //获取免费提现推广数据 参数 page pagesize 
    SC_GET_FREE_CASH_INVITATION_P: 39,      //返回免费提现推广数据
    //查询破产
    CS_FETCH_BROKE_AWARD_P: 40,               //获取或领取破产补助 参数 isfetch 0 获取 1 领取 
    SC_FETCH_BROKE_AWARD_P: 41,               //返回破产补助 返回结构 fetchcount 已领取次数 awardcase 可领取金额
    //邮件
    CS_FETCH_MAIL_AWARD_P: 42,               //领取邮件奖励 参数 fetchid -1 领取并已读所有 其他 指定邮件ID 
    SC_FETCH_MAIL_AWARD_P: 43,               //返回邮件奖励 返回结构 fetchbonus 领取到的积分 fetchcash 领取到的金币

    CS_BIND_PHONE_USER_P: 44,               //绑定用户手机号码 参数 phone checkcode 
    SC_BIND_PHONE_USER_P: 45,               //返回状态 
    CS_BIND_MAIL_USER_P: 46,               //绑定用户邮箱 参数 name mail 
    SC_BIND_MAIL_USER_P: 47,               //返回状态
    CS_FETCH_CDKEY_AWARD_P: 48,               //领取CDKEY奖励 参数 cdkey 
    SC_FETCH_CDKEY_AWARD_P: 49,               //返回状态

    CD_SHARING_SUCCESS_P :199,                //发送分享成功
    DC_SHARING_SUCCESS_P :200,                //返回分享成功
    CD_QUERY_BENEFIT_P :201,                  //查询救济金
    DC_QUERY_BENEFIT_P :202,                  //返回救济金信息
    CD_RECEIVE_BENEFIT_P :203,                //领取救济金
    DC_RECEIVE_BENEFIT_P :204,                //返回救济金结果
}
                                       
