import { GameMode } from "../common/Define";

//////////////////////////////////////////////////////////////////////////////////
if (window["ENABLE_CHINESE"] === undefined) {
    window["ENABLE_CHINESE"] = CC_PREVIEW;
}
//////////////////////////////////////////////////////////////////////////////////
// 配置类
export default class Config  {
    //////////////////////////////////////////////////////////////////////////////
    // 设计分辨率大小
    public static BACKGROUP_WIDTH = 1440;
    public static SCREEN_WIDTH = 1280;
    public static SCREEN_HEIGHT = 720;

    // 渠道ID
    public static CHANNEL_ID = 501;

    // 间隔多久重新检测游戏更新(秒)
    public static CHECK_UPDATE_INTERVAL = 100;

    //金币比例
    public static SCORE_RATE=100;

    // 服务器列表
    public static SERVER_LIST = [
		'http://localhost:4000/serverlist_v2?'
        //'https://sl1.teenpatti4.top/serverlist_v2?'
        // 'https://sl.Teenpatti21.com/serverlist_v2?',
        // 'https://sl.Teenpatti21.com/serverlist_v2?',
        // 'https://sl.Teenpatti21.com/serverlist_v2?'
    ];
    
    //////////////////////////////////////////////////////////////////////////////
    // 游戏列表
    public static GAME_LIST = {
        teenpatti: {gameId: 1,showNotice:true, matchPos:cc.v2(-270,315),widgetLeft:320, name: 'teenpatti',aniName:'Tp', iconname: 'hall/images/games/font_teenPatti', landscape: true, gameMode: GameMode.DEFAUTL, showName: 'teenpatti', desc: 'Teen patti',roomMoney:[10,20,30]}, 
        longhu: {gameId: 22,showNotice:false, matchPos:cc.v2(-400,315),widgetLeft:190, name: 'longhu',aniName:'DvsT', iconname: 'hall/images/games/icon_longhu_name', landscape: true, gameMode: GameMode.DEFAUTL, showName: 'dragonVStiger', desc: 'DragonTiger',roomMoney:[]},
        tpak47: {gameId: 2,showNotice:true, matchPos:cc.v2(-270,315), name: 'tpak47',aniName:'', iconname: 'hall/images/games/icon_tpak47', landscape: true, gameMode: GameMode.DEFAUTL, showName: 'tpak47', desc: 'tpak47',roomMoney:[]}, 
        slotniu: {gameId: 41, showNotice:false,matchPos:cc.v2(-400,315), name: "slotniu",aniName:'SoW', iconname: 'hall/images/games/icon_wild_name', landscape: true, gameMode: GameMode.DEFAUTL, showName: 'Safari Wealth', desc: 'SafariWealth',roomMoney:[]}, 
        tppotblind: {gameId: 3,showNotice:true, matchPos:cc.v2(-270,315), name: 'tppotblind',aniName:'PotBlind', iconname: 'hall/images/games/icon_potblind_name', landscape: true, gameMode: GameMode.DEFAUTL, showName: 'Pot Blind', desc: 'Pot Blind',roomMoney:[]}, 
        slot777: {gameId: 40,showNotice:false, matchPos:cc.v2(-284,315), name: 'slot777',aniName:'SLOT', iconname: 'hall/images/games/icon_slot_name', landscape: true, gameMode: GameMode.DEFAUTL, showName: 'slot777', desc: 'slot777',roomMoney:[]},  
        slotcat: {gameId: 42, showNotice:false,matchPos:cc.v2(-284,315), name: 'slotcat',aniName:'ExplorerSlots', iconname: 'hall/images/games/icon_explorerslots_name', landscape: true, gameMode: GameMode.DEFAUTL, showName: 'explorerslots', desc: 'explorerslots',roomMoney:[]}, 
        andarbahar: {gameId: 11,showNotice:true,matchPos:cc.v2(-284,315), name: 'andarbahar',aniName:'', iconname: 'hall/images/games/icon_andarbahar', landscape: true, gameMode: GameMode.DEFAUTL, showName: 'Andar Bahar', desc: 'Andar Bahar',roomMoney:[]}, 
        rummy: {gameId: 12, showNotice:true,matchPos:cc.v2(-284,315), name: 'rummy',aniName:'PointRM', iconname: 'hall/images/games/icon_rummy_name', landscape: true, gameMode: GameMode.DEFAUTL, showName: 'Point Rummy', desc: 'Point Rummy',roomMoney:[]}, 
        crash: {gameId: 23,showNotice:true, matchPos:cc.v2(-284,315), name: 'crash',aniName:'CRASH', iconname: 'hall/images/games/icon_crash_name', landscape: true, gameMode: GameMode.DEFAUTL, showName: 'crash', desc: 'crash',roomMoney:[]}, 
        fishprawncrab: {gameId: 21,showNotice:false, matchPos:cc.v2(-284,315), name: 'fishprawncrab',aniName:'YXX', iconname: 'hall/images/games/DT_YXX', landscape: true, gameMode: GameMode.DEFAUTL, showName: 'fishprawncrab', desc: 'fishprawncrab',roomMoney:[]}, 
        //新增加4个
        cardroulette: {gameId: 24,showNotice:false, matchPos:cc.v2(-284,315), name: 'cardroulette',aniName:'CardR', iconname: 'hall/images/games/icon_cardroulette', landscape: true, gameMode: GameMode.DEFAUTL, showName: 'cardroulette', desc: 'cardroulette'}, 
        letsparty: {gameId: 43,showNotice:false, matchPos:cc.v2(-284,315), name: 'letsparty',aniName:'LetsParty', iconname: 'hall/images/games/icon_letsparty', landscape: true, gameMode: GameMode.DEFAUTL, showName: 'letsparty', desc: 'letsparty'}, 
        sevenupdown: {gameId: 25,showNotice:false, matchPos:cc.v2(-284,315), name: 'sevenupdown',aniName:'', iconname: 'hall/images/games/icon_7updown', landscape: true, gameMode: GameMode.DEFAUTL, showName: '7updown', desc: '7updown'}, 
        threepattiwar: {gameId: 26,showNotice:false, matchPos:cc.v2(-284,315), name: 'threepattiwar',aniName:'', iconname: 'hall/images/games/iocn_3pattiwar', landscape: true, gameMode: GameMode.DEFAUTL, showName: '3pattiwar', desc: '3pattiwar'}, 
        moreGame: {gameId: 0,showNotice:false, name: 'moreGame', iconname: 'hall/images/games/DT_more_txt', landscape: true, gameMode: GameMode.DEFAUTL, showName: 'moreGame', desc: 'moreGame',roomMoney:[]}, 
    };
    
    //////////////////////////////////////////////////////////////////////////////
    // 默认更新地址
    public static DEFAULT_UPDATE_URL = '';
    // 默认支付地址
    public static DEFAULT_PAY_URL = '';
    // 默认游戏规则地址
    public static DEFAULT_GAMERULE_URL = '';
    // iOS支付服务器地址
    public static DEFAULT_IOSPAY_URL = '';
    // 默认客服地址
    public static DEFAULT_SERVICE_URL = '';

    //////////////////////////////////////////////////////////////////////////////

    //appsfly
    public static APPSFLY_KEY = '';
    public static APPLEAPPID = '';

    //adjust
    public static ADJUST_TOKEN = '9nonpb5c2nls';

}
