defmodule Cypridina.Repo.Migrations.DropCommissionRecords do
  @moduledoc """
  删除 commission_records 表

  CommissionRecord 模型已被完全移除，相关功能通过 PointsTransactionHelper 实现
  """

  use Ecto.Migration

  def up do
    # 删除外键约束
    drop_if_exists constraint(:commission_records, "commission_records_agent_id_fkey")
    drop_if_exists constraint(:commission_records, "commission_records_subordinate_id_fkey")

    # 删除表
    drop_if_exists table(:commission_records)
  end

  def down do
    # 重新创建 commission_records 表（如果需要回滚）
    create table(:commission_records, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true

      add :agent_id,
          references(:users,
            column: :id,
            name: "commission_records_agent_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :subordinate_id,
          references(:users,
            column: :id,
            name: "commission_records_subordinate_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :transaction_type, :text, null: false
      add :transaction_id, :uuid, null: false
      add :original_amount, :bigint, null: false
      add :commission_rate, :decimal, null: false
      add :commission_amount, :bigint, null: false
      add :status, :bigint, null: false, default: 1

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end
  end
end
