defmodule <PERSON><PERSON><PERSON><PERSON>.MixProject do
  use Mix.Project

  def project do
    [
      app: :cypridina,
      version: "0.1.0",
      elixir: "~> 1.18",
      listeners: [Phoenix.CodeReloader],
      elixirc_paths: elixirc_paths(Mix.env()),
      start_permanent: Mix.env() == :prod,
      consolidate_protocols: Mix.env() != :dev,
      aliases: aliases(),
      deps: deps()
    ]
  end

  # Configuration for the OTP application.
  #
  # Type `mix help compile.app` for more information.
  def application do
    [
      mod: {Cypridina.Application, []},
      extra_applications: extra_applications(Mix.env(), [:logger, :runtime_tools])
    ]
  end

  defp extra_applications(:dev, default), do: default ++ [:lettuce]
  defp extra_applications(_, default), do: default

  # Specifies which paths to compile per environment.
  defp elixirc_paths(:test), do: ["lib", "test/support"]
  defp elixirc_paths(_), do: ["lib"]

  # Specifies your project dependencies.
  #
  # Type `mix help deps` for examples and options.
  defp deps do
    [
      {:tidewave, "~> 0.1", only: [:dev]},
      {:ash_ai, "~> 0.1.8"},
      {:bcrypt_elixir, "~> 3.0"},
      {:open_api_spex, "~> 3.0"},
      {:picosat_elixir, "~> 0.2"},
      {:absinthe_phoenix, "~> 2.0"},
      {:ash_cloak, "~> 0.1"},
      {:cloak, "~> 1.0"},
      {:ash_paper_trail, "~> 0.5"},
      {:mishka_chelekom, "~> 0.0", only: [:dev]},
      # {:live_debugger, "~> 0.1", only: [:dev]},
      {:ash_archival, "~> 1.0"},
      # {:ash_double_entry, "~> 1.0"},
      # {:ash_money, "~> 0.2"},
      {:ash_state_machine, "~> 0.2"},
      {:oban_web, "~> 2.0"},
      {:ash_oban, "~> 0.4"},
      {:ash_admin, "~> 0.13"},
      {:ash_csv, "~> 0.9"},
      {:ash_authentication_phoenix, "~> 2.0"},
      {:ash_authentication, "~> 4.0"},
      {:ash_sqlite, "~> 0.2"},
      {:ash_json_api, "~> 1.0"},
      {:ash_graphql, "~> 1.0"},
      {:oban, "~> 2.0"},
      {:sourceror, "~> 1.8", only: [:dev, :test]},
      {:ash_phoenix, "~> 2.0"},
      {:ash_postgres, "~> 2.0"},
      {:ash, "~> 3.0"},
      {:igniter, "~> 0.5", only: [:dev, :test]},
      {:phoenix, "~> 1.8.0-rc.3", override: true},
      {:phoenix_ecto, "~> 4.5"},
      {:ecto_sql, "~> 3.10"},
      {:postgrex, ">= 0.0.0"},
      {:phoenix_html, "~> 4.1"},
      {:phoenix_live_reload, "~> 1.2", only: :dev},
      {:phoenix_live_view, "~> 1.0.9"},
      {:floki, ">= 0.30.0", only: :test},
      {:phoenix_live_dashboard, "~> 0.8.3"},
      {:esbuild, "~> 0.9", runtime: Mix.env() == :dev},
      {:tailwind, "~> 0.3", runtime: Mix.env() == :dev},
      # {:heroicons,
      #  path: "deps/heroicons", sparse: "optimized", app: false, compile: false, depth: 1},
      {:heroicons,
       github: "tailwindlabs/heroicons",
       tag: "v2.2.0",
       sparse: "optimized",
       app: false,
       compile: false,
       depth: 1},
      {:swoosh, "~> 1.16"},
      {:req, "~> 0.5"},
      {:telemetry_metrics, "~> 1.0"},
      {:telemetry_poller, "~> 1.0"},
      {:gettext, "~> 0.26"},
      {:jason, "~> 1.2"},
      {:dns_cluster, "~> 0.1.1"},
      {:bandit, "~> 1.5"},

      # 自行添加的依赖
      # 跨域插件
      {:corsica, "~> 2.0"},
      # 代码自动刷新插件
      {:lettuce, "~> 0.3.0", only: :dev},
      # 时区
      {:tzdata, "~> 1.1"},
      # 错误跟踪
      {:error_tracker, "~> 0.6"},
      # 后台管理
      {:backpex, "~> 0.13.0"},
      # 显式添加money依赖，并排除冲突模块
      {:money, "~> 1.14", override: true},
      # MessagePack 序列化支持
      {:msgpax, "~> 2.4"},
      {:msgpax_helper, github: "aisrael/msgpax_helper", tag: "0.2.1"},
      # 修复decimal版本冲突
      {:decimal, "~> 2.0", override: true}
      # 支持.env文件自动配置环境变量
      # {:dotenvy, "~> 1.0.0"}
    ]
  end

  # Aliases are shortcuts or tasks specific to the current project.
  # For example, to install project dependencies and perform other setup tasks, run:
  #
  #     $ mix setup
  #
  # See the documentation for `Mix` for more info on aliases.
  defp aliases do
    [
      setup: ["deps.get", "ash.setup", "assets.setup", "assets.build", "run priv/repo/seeds.exs"],
      "ecto.setup": ["ecto.create", "ecto.migrate", "run priv/repo/seeds.exs"],
      "ecto.reset": ["ecto.drop", "ecto.setup"],
      test: ["ash.setup --quiet", "test"],
      "assets.setup": [
        "tailwind.install --if-missing",
        "esbuild.install --if-missing",
        "cmd npm install --prefix assets"
      ],
      "assets.build": ["tailwind cypridina", "esbuild cypridina"],
      "assets.deploy": [
        "tailwind cypridina --minify",
        "esbuild cypridina --minify",
        "phx.digest"
      ]
    ]
  end
end
