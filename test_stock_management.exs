# 测试股票管理功能的脚本
# 使用方法: iex -S mix 然后运行 Code.eval_file("test_stock_management.exs")

alias RacingGame.StockManagementService

IO.puts("=== 股票管理功能测试 ===")

# 1. 获取当前股票持仓摘要
IO.puts("\n1. 获取股票持仓摘要:")
holdings_summary = StockManagementService.get_stock_holdings_summary()
IO.inspect(holdings_summary, label: "股票持仓摘要")

# 2. 获取股票统计摘要
IO.puts("\n2. 获取股票统计摘要:")
statistics_summary = StockManagementService.get_stock_statistics_summary()
IO.inspect(statistics_summary, label: "股票统计摘要")

# 3. 测试重置功能（注意：这会删除所有数据！）
IO.puts("\n3. 测试重置股票持仓功能:")
IO.puts("警告：这将删除所有股票持仓数据！")
IO.puts("如果要执行，请取消注释下面的代码")

# case StockManagementService.reset_all_stock_holdings() do
#   {:ok, _} ->
#     IO.puts("✅ 重置成功")
#   {:error, reason} ->
#     IO.puts("❌ 重置失败: #{reason}")
# end

# 4. 测试重新统计功能
IO.puts("\n4. 测试重新统计股票持仓功能:")
case StockManagementService.recalculate_stock_holdings_from_transactions() do
  {:ok, _} ->
    IO.puts("✅ 重新统计成功")
    
    # 重新获取摘要查看结果
    new_holdings_summary = StockManagementService.get_stock_holdings_summary()
    new_statistics_summary = StockManagementService.get_stock_statistics_summary()
    
    IO.puts("\n重新统计后的结果:")
    IO.inspect(new_holdings_summary, label: "新的股票持仓摘要")
    IO.inspect(new_statistics_summary, label: "新的股票统计摘要")
    
  {:error, reason} ->
    IO.puts("❌ 重新统计失败: #{reason}")
end

IO.puts("\n=== 测试完成 ===")
