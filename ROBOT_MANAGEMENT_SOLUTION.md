# 龙虎斗动态机器人管理系统

## 问题描述

原有的龙虎斗游戏机器人系统存在以下问题：
- 机器人积分耗尽后停止活动，没有清退机制
- 缺乏动态补充新机器人的功能
- 游戏进行一段时间后，机器人数量越来越少
- 缺乏持续性的机器人管理策略

## 解决方案

### 1. 动态机器人管理系统

实现了一个完整的动态机器人管理系统，包括：

#### 核心功能
- **自动清退**：清理积分不足的机器人
- **动态补充**：自动添加新机器人到目标数量
- **智能轮换**：定期轮换老机器人保持新鲜感
- **实时监控**：持续监控机器人状态

#### 管理策略
- **定时检查**：每30秒执行一次管理任务
- **积分阈值**：积分低于1000的机器人自动清理
- **轮换机制**：超过30分钟的机器人有10%概率被轮换
- **分层积分**：新机器人按不同策略生成积分

### 2. 实现细节

#### 新增函数

**LongHuAI模块新增：**
```elixir
# 动态管理主函数
def manage_robots_dynamically(state)

# 清理积分不足的机器人
def cleanup_broke_robots(state, min_money \\ 1000)

# 轮换老机器人
def rotate_old_robots(state, rotation_probability \\ 0.1)

# 统计机器人数量
def count_robots(state)

# 生成机器人初始积分
def generate_robot_initial_money()

# 添加单个机器人
def add_single_robot(state, index \\ 1)
```

**LongHuRoom模块增强：**
```elixir
# 处理机器人管理消息
def handle_info(:manage_robots, state)

# 增强的机器人下注处理（包含积分检查）
def handle_info({:robot_bet, robot_id}, state)
```

#### 触发时机

1. **房间初始化**：启动30秒定时器
2. **定时管理**：每30秒自动执行管理任务
3. **结算后清理**：游戏结算后立即清理积分不足机器人
4. **下注前检查**：机器人下注前检查积分状态

### 3. 积分生成策略

新机器人的积分按以下策略生成：
- **30%保守型**：5,000 - 20,000积分
- **40%中等型**：15,000 - 50,000积分  
- **30%富豪型**：40,000 - 100,000积分

### 4. 机器人个性化

每个机器人都有独特属性：
- **下注风格**：保守型、中庸型、激进型
- **偏好区域**：龙、虎、和
- **激进程度**：0-1的浮点数
- **下注频率**：80%-100%
- **创建时间**：用于轮换判断

## 技术优势

### 1. 自动化管理
- 无需人工干预
- 全自动运行
- 智能决策

### 2. 游戏体验
- 始终保持足够的机器人数量
- 避免"死"机器人影响游戏氛围
- 机器人多样性保持游戏新鲜感

### 3. 性能优化
- 定时任务分散执行
- 增量更新，避免大批量操作
- 内存友好的清理策略

### 4. 可配置性
- 机器人数量可配置
- 积分阈值可调整
- 轮换概率可设置
- 检查间隔可修改

## 使用效果

### 游戏活跃度
- ✅ 始终维持8个活跃机器人
- ✅ 机器人持续参与下注
- ✅ 游戏氛围始终热烈

### 机器人质量
- ✅ 及时清理积分不足的机器人
- ✅ 新机器人带来充足积分
- ✅ 多样化的下注行为

### 系统稳定性
- ✅ 自动恢复机器人数量
- ✅ 防止机器人数量递减
- ✅ 长期稳定运行

## 监控和日志

系统提供详细的日志记录：
```
🤖 [MANAGE_ROBOTS] 开始动态管理机器人
🤖 [CLEANUP_ROBOTS] 清理 2 个积分不足的机器人
🤖 [ADD_ROBOT] 添加新机器人: -1000009 (新龙虎高手) - 积分: 15000
🤖 [ROTATE_ROBOTS] 轮换 1 个老机器人
```

## 配置参数

```elixir
# 游戏配置
%{
  enable_robots: true,        # 启用机器人
  robot_count: 8,            # 目标机器人数量
  robot_check_interval: 30,  # 检查间隔（秒）
  min_robot_money: 1000,     # 最低积分要求
  rotation_probability: 0.1,  # 轮换概率
  max_robot_age: 30          # 最大年龄（分钟）
}
```

## 总结

这个动态机器人管理系统完全解决了原有的问题：
1. **持续清退**积分不足的机器人
2. **动态补充**新的带积分机器人
3. **智能轮换**保持游戏新鲜感
4. **自动化管理**无需人工干预

系统已经集成到龙虎斗游戏中，可以立即投入使用，为玩家提供更好的游戏体验。
