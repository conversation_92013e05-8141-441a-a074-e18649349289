defmodule CypridinaTest.CommissionTest do
  @moduledoc """
  测试抽水功能是否正确计算
  """
  use ExUnit.Case
  alias Cypridina.Accounts.AgentService

  describe "commission calculation" do
    test "calculate commission with 5% rate" do
      # 测试5%抽水比例
      commission_rate = Decimal.new("0.05")
      
      # 测试1000积分的抽水
      amount = 1000
      expected_commission = 50  # 1000 * 0.05 = 50
      
      actual_commission = amount
      |> Decimal.mult(commission_rate)
      |> Decimal.round(0, :down)
      |> Decimal.to_integer()
      
      assert actual_commission == expected_commission
    end
    
    test "calculate commission with 13% rate (max rate)" do
      # 测试13%抽水比例（最大比例）
      commission_rate = Decimal.new("0.13")
      
      # 测试1000积分的抽水
      amount = 1000
      expected_commission = 130  # 1000 * 0.13 = 130
      
      actual_commission = amount
      |> Decimal.mult(commission_rate)
      |> Decimal.round(0, :down)
      |> Decimal.to_integer()
      
      assert actual_commission == expected_commission
    end
    
    test "commission rounds down correctly" do
      # 测试向下取整
      commission_rate = Decimal.new("0.05")
      
      # 测试199积分的抽水，应该是9而不是10
      amount = 199
      expected_commission = 9  # 199 * 0.05 = 9.95，向下取整为9
      
      actual_commission = amount
      |> Decimal.mult(commission_rate)
      |> Decimal.round(0, :down)
      |> Decimal.to_integer()
      
      assert actual_commission == expected_commission
    end
    
    test "small amounts commission" do
      # 测试小金额的抽水
      commission_rate = Decimal.new("0.05")
      
      # 测试19积分的抽水，应该是0
      amount = 19
      expected_commission = 0  # 19 * 0.05 = 0.95，向下取整为0
      
      actual_commission = amount
      |> Decimal.mult(commission_rate)
      |> Decimal.round(0, :down)
      |> Decimal.to_integer()
      
      assert actual_commission == expected_commission
      
      # 测试20积分的抽水，应该是1
      amount = 20
      expected_commission = 1  # 20 * 0.05 = 1.0
      
      actual_commission = amount
      |> Decimal.mult(commission_rate)
      |> Decimal.round(0, :down)
      |> Decimal.to_integer()
      
      assert actual_commission == expected_commission
    end
  end
  
  describe "betting commission scenarios" do
    test "betting win scenario" do
      # 模拟下注获奖场景
      bet_amount = 100
      multiplier = 3
      gross_payout = bet_amount * multiplier  # 300积分奖金
      commission_rate = Decimal.new("0.05")
      
      # 计算抽水
      commission_amount = gross_payout
      |> Decimal.mult(commission_rate)
      |> Decimal.round(0, :down)
      |> Decimal.to_integer()
      
      actual_payout = gross_payout - commission_amount
      
      assert commission_amount == 15  # 300 * 0.05 = 15
      assert actual_payout == 285     # 300 - 15 = 285
    end
  end
  
  describe "stock trading commission scenarios" do
    test "stock selling scenario" do
      # 模拟股票卖出场景
      stock_quantity = 10
      stock_price = 160
      total_sale = stock_quantity * stock_price  # 1600积分
      commission_rate = Decimal.new("0.05")
      
      # 计算抽水
      commission_amount = total_sale
      |> Decimal.mult(commission_rate)
      |> Decimal.round(0, :down)
      |> Decimal.to_integer()
      
      actual_income = total_sale - commission_amount
      
      assert commission_amount == 80   # 1600 * 0.05 = 80
      assert actual_income == 1520     # 1600 - 80 = 1520
    end
  end
end
