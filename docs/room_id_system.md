# 房间号生成系统设计文档

## 概述

新的房间号生成系统支持两种ID：
1. **6位数房间码** - 用户友好的房间标识符
2. **唯一溯源码** - 系统内部追踪和调试用

## 功能特性

### 1. 双ID系统

#### 房间码 (Room ID)
- **格式**: 6位数字 (100000-999999)
- **用途**: 用户加入房间、显示给用户
- **特点**: 
  - 简短易记
  - 避免与现有房间冲突
  - 最多尝试100次生成唯一ID
  - 如果100次都冲突，使用时间戳后6位

#### 溯源码 (Trace ID)
- **格式**: `trace_{timestamp}_{random_hex}`
- **用途**: 系统内部追踪、日志记录、调试
- **特点**:
  - 全局唯一
  - 包含时间戳信息
  - 包含随机字符串

### 2. Topic生成

每个房间自动生成topic：`room:{game_id}_{room_id}`

**示例**:
- Teen Patti房间: `room:1_428114`
- 龙虎斗房间: `room:22_553356`

### 3. 游戏ID映射

系统自动根据game_type获取对应的game_id：

| Game Type | Game ID | 说明 |
|-----------|---------|------|
| teen_patti | 1 | Teen Patti游戏 |
| longhu | 22 | 龙虎斗游戏 |

## 实现细节

### 房间规格结构

```elixir
room_spec = %{
  id: "428114",                                    # 6位数房间码
  trace_id: "trace_1749699386352_bdcf768dda774fab", # 唯一溯源码
  game_type: :teen_patti,                          # 游戏类型
  game_id: 1,                                      # 游戏ID
  topic: "room:1_428114",                          # 房间topic
  creator_id: "user_123",                          # 创建者ID
  config: %{...},                                  # 游戏配置
  created_at: 1749699386352,                       # 创建时间戳
  status: :waiting                                 # 房间状态
}
```

### 统一处理

#### 所有游戏类型
- 统一使用6位数随机房间码（100000-999999）
- 包括百人场游戏（龙虎斗等）
- 确保与现有房间不冲突
- 都生成唯一溯源码用于追踪

## API变更

### 新增函数

```elixir
# 获取游戏ID
GameFactory.get_game_id(:teen_patti)  # => 1

# 获取房间列表
RoomManager.list_rooms()  # => [%{id: "428114", ...}, ...]
```

### 房间创建流程

1. 调用 `generate_room_id/1` 生成双ID
2. 根据game_type获取game_id
3. 生成topic: `"room:#{game_id}_#{room_id}"`
4. 创建完整的room_spec
5. 启动房间GenServer

## 使用示例

### 创建普通游戏房间

```elixir
{:ok, room_id} = RoomManager.create_room(:teen_patti, "user_123", %{})
# room_id: "428114"

{:ok, room_info} = RoomManager.get_room_info(room_id)
# room_info.topic: "room:1_428114"
# room_info.trace_id: "trace_1749699386352_bdcf768dda774fab"
```

### 匹配百人场房间

```elixir
{:ok, room_info} = RoomManager.match_room("user_123", 22, %{})
# room_info.room_id: "553356"
# room_info对应的topic: "room:22_553356"
```

## 优势

1. **用户友好**: 6位数房间码简短易记
2. **系统追踪**: 溯源码提供完整的追踪能力
3. **唯一性保证**: 多重机制确保ID不冲突
4. **统一格式**: 所有房间都使用6位数ID，包括百人场
5. **自动化**: Topic自动生成，无需手动配置

## 测试验证

系统已通过以下测试：
- ✅ 6位数房间码生成
- ✅ 房间ID唯一性验证
- ✅ 溯源码格式正确
- ✅ Topic自动生成
- ✅ 游戏ID映射正确
- ✅ 百人场房间统一格式
